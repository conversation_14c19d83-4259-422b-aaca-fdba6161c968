# RTOS学习笔记3 - 优先级

## 1. 任务优先级 (Task Priority)

### 概念
优先级决定了任务的调度顺序，是RTOS调度器的核心依据。高优先级任务总是优先于低优先级任务执行。
- **抢占式调度** - 高优先级任务可以抢占低优先级任务
- **数值表示** - 数值越大优先级越高（0为最低优先级）
- **动态调整** - 运行时可以修改任务优先级
- **继承机制** - 支持优先级继承解决优先级反转

### 优先级范围
- **最低优先级**：0 (空闲任务使用)
- **最高优先级**：configMAX_PRIORITIES - 1
- **用户可用**：1 到 (configMAX_PRIORITIES - 1)
- **典型配置**：configMAX_PRIORITIES = 5 或 8

### 优先级分配原则
1. **实时性要求** - 实时性要求越高，优先级越高
2. **响应时间** - 需要快速响应的任务优先级高
3. **重要性** - 系统关键任务优先级高
4. **频率考虑** - 高频任务不一定需要高优先级

### 核心API
- `vTaskPrioritySet(handle, priority)` - 设置任务优先级
- `uxTaskPriorityGet(handle)` - 获取任务优先级
- `vTaskPrioritySetFromISR(handle, priority)` - 中断中设置优先级

### CubeMX中的优先级配置
在STM32CubeMX中配置路径：**Middleware → FREERTOS → Config parameters**
- **MAX_PRIORITIES**: 8 (设置最大优先级数量)
- **IDLE_PRIORITY**: 0 (空闲任务优先级，通常为0)

### 典型优先级分配示例
```c
// 优先级分配示例 (假设MAX_PRIORITIES = 8)
#define PRIORITY_IDLE       0   // 空闲任务 (系统自动)
#define PRIORITY_LOW        1   // 低优先级任务 (日志、统计)
#define PRIORITY_NORMAL     2   // 普通任务 (数据处理)
#define PRIORITY_HIGH       3   // 高优先级任务 (控制逻辑)
#define PRIORITY_REALTIME   4   // 实时任务 (中断处理)
#define PRIORITY_CRITICAL   5   // 关键任务 (安全监控)
// 6-7 预留给系统任务或特殊需求
```

## 2. 调度策略

### 抢占式调度
- **立即抢占** - 高优先级任务就绪时立即抢占CPU
- **任务切换** - 自动保存/恢复任务上下文
- **实时响应** - 保证高优先级任务的实时性

### 同优先级调度
- **时间片轮转** - 同优先级任务按时间片轮流执行
- **协作式** - 任务主动让出CPU才切换
- **配置选项** - configUSE_TIME_SLICING控制是否启用时间片

### 调度时机
1. **任务阻塞** - 当前任务进入阻塞态
2. **任务完成** - 任务执行完毕或删除
3. **高优先级就绪** - 有更高优先级任务变为就绪态
4. **时间片到期** - 同优先级时间片轮转
5. **主动让出** - 调用taskYIELD()

## 3. 优先级反转问题

### 什么是优先级反转
当高优先级任务被低优先级任务间接阻塞，导致系统响应性能下降的现象。

### 经典场景
```
任务A (高优先级) 等待 互斥量
任务B (中优先级) 抢占CPU
任务C (低优先级) 持有 互斥量
结果：A被B间接阻塞，优先级关系颠倒
```

### 解决方案：优先级继承
- **临时提升** - 低优先级任务临时提升到高优先级
- **自动恢复** - 释放资源后自动恢复原优先级
- **互斥量支持** - FreeRTOS互斥量自动支持优先级继承

### 优先级继承示例
```c
// 使用互斥量避免优先级反转
SemaphoreHandle_t xMutex;

void HighPriorityTask(void *pvParameters) {
    for (;;) {
        // 等待互斥量，如果被低优先级任务持有
        // 低优先级任务会临时提升优先级
        if (xSemaphoreTake(xMutex, portMAX_DELAY) == pdTRUE) {
            // 访问共享资源
            AccessSharedResource();
            xSemaphoreGive(xMutex);
        }
    }
}
```

## 4. 优先级设计最佳实践

### 1. 分层设计
```c
// 系统分层优先级设计
#define LAYER_SYSTEM        6-7     // 系统服务 (定时器、看门狗)
#define LAYER_CRITICAL      4-5     // 关键任务 (安全、通信)
#define LAYER_CONTROL       2-3     // 控制逻辑 (算法、决策)
#define LAYER_APPLICATION   1       // 应用任务 (UI、日志)
#define LAYER_BACKGROUND    0       // 后台任务 (空闲)
```

### 2. 响应时间导向
```c
// 按响应时间要求分配
#define PRIORITY_INTERRUPT_HANDLER  5   // <1ms 中断后处理
#define PRIORITY_CONTROL_LOOP       4   // <10ms 控制回路
#define PRIORITY_COMMUNICATION      3   // <100ms 通信处理
#define PRIORITY_USER_INTERFACE     2   // <500ms UI响应
#define PRIORITY_DATA_LOGGING       1   // >1s 数据记录
```

### 3. 避免优先级过多
```c
// 推荐：使用较少的优先级等级
#define PRIORITY_HIGH       3   // 高优先级
#define PRIORITY_MEDIUM     2   // 中优先级  
#define PRIORITY_LOW        1   // 低优先级
// 简单清晰，易于管理
```

### 4. 预留优先级
```c
// 为未来扩展预留优先级
#define PRIORITY_RESERVED_HIGH  6-7  // 预留给紧急任务
#define PRIORITY_CURRENT_MAX    5    // 当前使用的最高优先级
```

## 5. 优先级调试技巧

### 1. 优先级监控
```c
void PrintTaskPriorities(void) {
    TaskHandle_t xHandle;
    UBaseType_t uxPriority;
    
    // 获取当前任务优先级
    xHandle = xTaskGetCurrentTaskHandle();
    uxPriority = uxTaskPriorityGet(xHandle);
    printf("Current task priority: %lu\r\n", uxPriority);
}
```

### 2. 运行时调整
```c
void AdjustTaskPriority(TaskHandle_t xTask, UBaseType_t uxNewPriority) {
    UBaseType_t uxCurrentPriority = uxTaskPriorityGet(xTask);
    
    if (uxCurrentPriority != uxNewPriority) {
        vTaskPrioritySet(xTask, uxNewPriority);
        printf("Priority changed from %lu to %lu\r\n", 
               uxCurrentPriority, uxNewPriority);
    }
}
```

### 3. 优先级反转检测
```c
// 监控互斥量等待时间，检测可能的优先级反转
TickType_t xStartTime = xTaskGetTickCount();
if (xSemaphoreTake(xMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
    TickType_t xWaitTime = xTaskGetTickCount() - xStartTime;
    if (xWaitTime > pdMS_TO_TICKS(50)) {
        printf("Long mutex wait detected: %lu ms\r\n", 
               xWaitTime * portTICK_PERIOD_MS);
    }
    xSemaphoreGive(xMutex);
}
```

## 6. 常见优先级问题

### 1. 优先级设置过高
- **问题**：所有任务都设置高优先级
- **后果**：失去优先级调度的意义
- **解决**：合理分配，大部分任务使用中低优先级

### 2. 优先级不足
- **问题**：关键任务优先级设置过低
- **后果**：实时性要求无法满足
- **解决**：根据实时性要求合理提升优先级

### 3. 优先级反转
- **问题**：使用二值信号量保护资源
- **后果**：高优先级任务被低优先级任务阻塞
- **解决**：使用互斥量替代二值信号量

### 4. 饥饿现象
- **问题**：低优先级任务长期得不到执行
- **后果**：系统功能不完整
- **解决**：合理设计任务阻塞点，避免高优先级任务长期占用CPU
