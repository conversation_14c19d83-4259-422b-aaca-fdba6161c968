Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f103xe.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xe.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xe.o(RESET) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.TIM1_UP_IRQHandler) for TIM1_UP_IRQHandler
    startup_stm32f103xe.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xe.o(.text) refers to startup_stm32f103xe.o(HEAP) for Heap_Mem
    startup_stm32f103xe.o(.text) refers to startup_stm32f103xe.o(STACK) for Stack_Mem
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to iwdg.o(i.MX_IWDG_Init) for MX_IWDG_Init
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os.o(i.osKernelStart) for osKernelStart
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    freertos.o(i.DisplayRefreshCallback) refers to freertos.o(i.Display_Refresh) for Display_Refresh
    freertos.o(i.Display_Clear) refers to cmsis_os.o(i.osMutexWait) for osMutexWait
    freertos.o(i.Display_Clear) refers to cmsis_os.o(i.osMutexRelease) for osMutexRelease
    freertos.o(i.Display_Clear) refers to freertos.o(.data) for .data
    freertos.o(i.Display_Refresh) refers to freertos.o(i.HC595_SendByte) for HC595_SendByte
    freertos.o(i.Display_Refresh) refers to freertos.o(i.HC138_SelectDigit) for HC138_SelectDigit
    freertos.o(i.Display_Refresh) refers to freertos.o(.data) for .data
    freertos.o(i.Display_SetDigit) refers to cmsis_os.o(i.osMutexWait) for osMutexWait
    freertos.o(i.Display_SetDigit) refers to cmsis_os.o(i.osMutexRelease) for osMutexRelease
    freertos.o(i.Display_SetDigit) refers to freertos.o(.data) for .data
    freertos.o(i.Display_SetNumber) refers to cmsis_os.o(i.osMutexWait) for osMutexWait
    freertos.o(i.Display_SetNumber) refers to cmsis_os.o(i.osMutexRelease) for osMutexRelease
    freertos.o(i.Display_SetNumber) refers to freertos.o(.data) for .data
    freertos.o(i.Display_SetString) refers to cmsis_os.o(i.osMutexWait) for osMutexWait
    freertos.o(i.Display_SetString) refers to strlen.o(.text) for strlen
    freertos.o(i.Display_SetString) refers to cmsis_os.o(i.osMutexRelease) for osMutexRelease
    freertos.o(i.Display_SetString) refers to freertos.o(.data) for .data
    freertos.o(i.HC138_SelectDigit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.HC595_SendByte) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os.o(i.osMutexCreate) for osMutexCreate
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os.o(i.osSemaphoreCreate) for osSemaphoreCreate
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os.o(i.osTimerCreate) for osTimerCreate
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os.o(i.osMessageCreate) for osMessageCreate
    freertos.o(i.MX_FREERTOS_Init) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os.o(i.osThreadCreate) for osThreadCreate
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.StartDemoControlTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartDemoControlTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartDemoControlTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.StartDisplayTask) refers to freertos.o(i.Display_Clear) for Display_Clear
    freertos.o(i.StartDisplayTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartDisplayTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartKeyTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartKeyTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartLedControlTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartLedControlTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.StartMonitorTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartMonitorTask) refers to cmsis_os.o(i.osDelay) for osDelay
    freertos.o(i.vApplicationGetIdleTaskMemory) refers to freertos.o(.bss) for .bss
    freertos.o(i.vApplicationGetTimerTaskMemory) refers to freertos.o(.bss) for .bss
    freertos.o(.constdata) refers to freertos.o(i.LedTimerCallback) for LedTimerCallback
    freertos.o(.constdata) refers to freertos.o(i.BeepTimerCallback) for BeepTimerCallback
    freertos.o(.constdata) refers to freertos.o(i.WatchdogTimerCallback) for WatchdogTimerCallback
    freertos.o(.constdata) refers to freertos.o(i.DisplayRefreshCallback) for DisplayRefreshCallback
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    freertos.o(.constdata) refers to freertos.o(i.StartMonitorTask) for StartMonitorTask
    freertos.o(.constdata) refers to freertos.o(i.StartDisplayTask) for StartDisplayTask
    freertos.o(.constdata) refers to freertos.o(i.StartLedControlTask) for StartLedControlTask
    freertos.o(.constdata) refers to freertos.o(i.StartKeyTask) for StartKeyTask
    freertos.o(.constdata) refers to freertos.o(i.StartDemoControlTask) for StartDemoControlTask
    iwdg.o(i.MX_IWDG_Init) refers to stm32f1xx_hal_iwdg.o(i.HAL_IWDG_Init) for HAL_IWDG_Init
    iwdg.o(i.MX_IWDG_Init) refers to main.o(i.Error_Handler) for Error_Handler
    iwdg.o(i.MX_IWDG_Init) refers to iwdg.o(.bss) for .bss
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM1_UP_IRQHandler) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim1
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_iwdg.o(i.HAL_IWDG_Init) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memset.o(.text) for memset
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to freertos.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to freertos.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os.o(i.osKernelRunning) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os.o(i.osKernelSysTick) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osKernelSysTick) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os.o(i.osKernelSysTick) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os.o(i.osMailAlloc) refers to cmsis_os.o(i.osPoolAlloc) for osPoolAlloc
    cmsis_os.o(i.osMailCAlloc) refers to cmsis_os.o(i.osMailAlloc) for osMailAlloc
    cmsis_os.o(i.osMailCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os.o(i.osMailCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os.o(i.osMailCreate) refers to cmsis_os.o(i.osPoolCreate) for osPoolCreate
    cmsis_os.o(i.osMailCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os.o(i.osMailFree) refers to cmsis_os.o(i.osPoolFree) for osPoolFree
    cmsis_os.o(i.osMailGet) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMailGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osMailGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os.o(i.osMailPut) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMailPut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os.o(i.osMailPut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osMessageAvailableSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os.o(i.osMessageCreate) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os.o(i.osMessageCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os.o(i.osMessageDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessageDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os.o(i.osMessageGet) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessageGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osMessageGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os.o(i.osMessagePeek) refers to queue.o(i.xQueuePeek) for xQueuePeek
    cmsis_os.o(i.osMessagePut) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessagePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os.o(i.osMessagePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osMessageWaiting) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMessageWaiting) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os.o(i.osMessageWaiting) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os.o(i.osMutexCreate) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os.o(i.osMutexCreate) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os.o(i.osMutexDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os.o(i.osMutexRelease) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osMutexWait) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osMutexWait) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osMutexWait) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os.o(i.osPoolAlloc) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osPoolAlloc) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os.o(i.osPoolAlloc) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os.o(i.osPoolCAlloc) refers to cmsis_os.o(i.osPoolAlloc) for osPoolAlloc
    cmsis_os.o(i.osPoolCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os.o(i.osPoolCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os.o(i.osSemaphoreCreate) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osSemaphoreDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os.o(i.osSemaphoreRelease) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os.o(i.osSemaphoreWait) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSemaphoreWait) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os.o(i.osSemaphoreWait) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os.o(i.osSignalSet) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSignalSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os.o(i.osSignalSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os.o(i.osSignalWait) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osSignalWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os.o(i.osSystickHandler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os.o(i.osSystickHandler) refers to port.o(i.SysTick_Handler) for SysTick_Handler
    cmsis_os.o(i.osThreadCreate) refers to cmsis_os.o(i.makeFreeRtosPriority) for makeFreeRtosPriority
    cmsis_os.o(i.osThreadCreate) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os.o(i.osThreadCreate) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os.o(i.osThreadGetPriority) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGetFromISR) for uxTaskPriorityGetFromISR
    cmsis_os.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os.o(i.osThreadGetPriority) refers to cmsis_os.o(i.makeCmsisPriority) for makeCmsisPriority
    cmsis_os.o(i.osThreadResume) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osThreadResume) refers to tasks.o(i.xTaskResumeFromISR) for xTaskResumeFromISR
    cmsis_os.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os.o(i.osThreadResumeAll) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os.o(i.osThreadSetPriority) refers to cmsis_os.o(i.makeFreeRtosPriority) for makeFreeRtosPriority
    cmsis_os.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os.o(i.osThreadSuspendAll) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os.o(i.osTimerCreate) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os.o(i.osTimerCreate) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os.o(i.osTimerDelete) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os.o(i.osTimerStart) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os.o(i.osTimerStop) refers to cmsis_os.o(i.inHandlerMode) for inHandlerMode
    cmsis_os.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.SysTick_Handler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvStartFirstTask
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xe.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing freertos.o(i.Display_SetDigit), (48 bytes).
    Removing freertos.o(i.Display_SetNumber), (84 bytes).
    Removing freertos.o(i.Display_SetString), (132 bytes).
    Removing iwdg.o(.rev16_text), (4 bytes).
    Removing iwdg.o(.revsh_text), (4 bytes).
    Removing iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_InitTick), (64 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (32 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (184 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (316 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort), (70 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT), (304 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (128 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (584 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (124 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (988 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_iwdg.o(i.HAL_IWDG_Refresh), (12 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (436 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (248 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (180 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (224 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start), (180 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (224 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig), (108 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (108 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (148 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (204 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (160 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (124 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (172 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (372 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (212 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (172 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (372 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (212 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (20 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (74 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (64 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (44 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(i.xEventGroupSync), (204 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (264 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (36 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (22 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (40 bytes).
    Removing queue.o(i.vQueueDelete), (46 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (40 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (30 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (34 bytes).
    Removing queue.o(i.xQueuePeek), (308 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (116 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (66 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (140 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (58 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (130 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (60 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (34 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (108 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (122 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (34 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (48 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (78 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (212 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (144 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (66 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (258 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (142 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (38 bytes).
    Removing tasks.o(i.pcTaskGetName), (32 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (52 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (104 bytes).
    Removing tasks.o(i.ulTaskNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (44 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelete), (172 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (28 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (180 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (196 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (112 bytes).
    Removing tasks.o(i.vTaskResume), (124 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (48 bytes).
    Removing tasks.o(i.vTaskSuspend), (188 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (48 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (224 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (260 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (16 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (140 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (136 bytes).
    Removing timers.o(i.pcTimerGetName), (22 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (36 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (48 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (54 bytes).
    Removing timers.o(i.vTimerSetTimerID), (38 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (22 bytes).
    Removing timers.o(i.xTimerGetPeriod), (22 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (32 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (48 bytes).
    Removing cmsis_os.o(.rev16_text), (4 bytes).
    Removing cmsis_os.o(.revsh_text), (4 bytes).
    Removing cmsis_os.o(.rrx_text), (6 bytes).
    Removing cmsis_os.o(i.makeCmsisPriority), (14 bytes).
    Removing cmsis_os.o(i.osAbortDelay), (4 bytes).
    Removing cmsis_os.o(i.osDelayUntil), (4 bytes).
    Removing cmsis_os.o(i.osKernelRunning), (18 bytes).
    Removing cmsis_os.o(i.osKernelSysTick), (24 bytes).
    Removing cmsis_os.o(i.osMailAlloc), (12 bytes).
    Removing cmsis_os.o(i.osMailCAlloc), (32 bytes).
    Removing cmsis_os.o(i.osMailCreate), (92 bytes).
    Removing cmsis_os.o(i.osMailFree), (12 bytes).
    Removing cmsis_os.o(i.osMailGet), (144 bytes).
    Removing cmsis_os.o(i.osMailPut), (92 bytes).
    Removing cmsis_os.o(i.osMessageAvailableSpace), (4 bytes).
    Removing cmsis_os.o(i.osMessageDelete), (24 bytes).
    Removing cmsis_os.o(i.osMessageGet), (148 bytes).
    Removing cmsis_os.o(i.osMessagePeek), (74 bytes).
    Removing cmsis_os.o(i.osMessagePut), (92 bytes).
    Removing cmsis_os.o(i.osMessageWaiting), (30 bytes).
    Removing cmsis_os.o(i.osMutexDelete), (24 bytes).
    Removing cmsis_os.o(i.osPoolAlloc), (106 bytes).
    Removing cmsis_os.o(i.osPoolCAlloc), (16 bytes).
    Removing cmsis_os.o(i.osPoolCreate), (94 bytes).
    Removing cmsis_os.o(i.osPoolFree), (48 bytes).
    Removing cmsis_os.o(i.osRecursiveMutexCreate), (4 bytes).
    Removing cmsis_os.o(i.osRecursiveMutexRelease), (4 bytes).
    Removing cmsis_os.o(i.osRecursiveMutexWait), (4 bytes).
    Removing cmsis_os.o(i.osSemaphoreDelete), (24 bytes).
    Removing cmsis_os.o(i.osSemaphoreGetCount), (4 bytes).
    Removing cmsis_os.o(i.osSemaphoreRelease), (80 bytes).
    Removing cmsis_os.o(i.osSemaphoreWait), (100 bytes).
    Removing cmsis_os.o(i.osSignalSet), (92 bytes).
    Removing cmsis_os.o(i.osSignalWait), (86 bytes).
    Removing cmsis_os.o(i.osSystickHandler), (20 bytes).
    Removing cmsis_os.o(i.osThreadGetId), (4 bytes).
    Removing cmsis_os.o(i.osThreadGetPriority), (32 bytes).
    Removing cmsis_os.o(i.osThreadList), (4 bytes).
    Removing cmsis_os.o(i.osThreadResume), (52 bytes).
    Removing cmsis_os.o(i.osThreadResumeAll), (18 bytes).
    Removing cmsis_os.o(i.osThreadSetPriority), (22 bytes).
    Removing cmsis_os.o(i.osThreadSuspend), (10 bytes).
    Removing cmsis_os.o(i.osThreadSuspendAll), (10 bytes).
    Removing cmsis_os.o(i.osThreadTerminate), (10 bytes).
    Removing cmsis_os.o(i.osThreadYield), (24 bytes).
    Removing cmsis_os.o(i.osTimerDelete), (44 bytes).
    Removing cmsis_os.o(i.osTimerStart), (92 bytes).
    Removing cmsis_os.o(i.osTimerStop), (88 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (108 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (32 bytes).

473 unused section(s) (total 32436 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/iwdg.c                       0x00000000   Number         0  iwdg.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_iwdg.c 0x00000000   Number         0  stm32f1xx_hal_iwdg.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS/cmsis_os.c 0x00000000   Number         0  cmsis_os.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\iwdg.c                       0x00000000   Number         0  iwdg.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_iwdg.c 0x00000000   Number         0  stm32f1xx_hal_iwdg.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS\cmsis_os.c 0x00000000   Number         0  cmsis_os.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c 0x00000000   Number         0  port.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xe.s                    0x00000000   Number         0  startup_stm32f103xe.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f103xe.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x080001cc   Section      150  port.o(.emb_text)
    .text                                    0x08000264   Section       64  startup_stm32f103xe.o(.text)
    .text                                    0x080002a4   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800032e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000392   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080003e0   Section        0  heapauxi.o(.text)
    .text                                    0x080003e6   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000430   Section        0  exit.o(.text)
    .text                                    0x08000444   Section        8  libspace.o(.text)
    .text                                    0x0800044c   Section        0  sys_exit.o(.text)
    .text                                    0x08000458   Section        2  use_no_semi.o(.text)
    .text                                    0x0800045a   Section        0  indicate_semi.o(.text)
    i.BeepTimerCallback                      0x0800045a   Section        0  freertos.o(i.BeepTimerCallback)
    i.BusFault_Handler                       0x0800045c   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x0800045e   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.DisplayRefreshCallback                 0x08000460   Section        0  freertos.o(i.DisplayRefreshCallback)
    i.Display_Clear                          0x08000464   Section        0  freertos.o(i.Display_Clear)
    i.Display_Refresh                        0x08000494   Section        0  freertos.o(i.Display_Refresh)
    i.Error_Handler                          0x080004e0   Section        0  main.o(i.Error_Handler)
    i.HAL_GPIO_Init                          0x080004e4   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x080006dc   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x080006ec   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080006f8   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IWDG_Init                          0x08000704   Section        0  stm32f1xx_hal_iwdg.o(i.HAL_IWDG_Init)
    i.HAL_IncTick                            0x08000764   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000774   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000798   Section        0  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000824   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000858   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000874   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080008b4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080008d8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x08000a04   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetPCLK2Freq                   0x08000a44   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000a64   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000ab0   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_TIMEx_BreakCallback                0x08000dd0   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08000dd2   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_Base_Init                      0x08000dd4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08000e2e   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08000e30   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x08000e9c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08000e9e   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08000fce   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08000fd0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PeriodElapsedCallback          0x08000fd4   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08000fe8   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HC138_SelectDigit                      0x08000fec   Section        0  freertos.o(i.HC138_SelectDigit)
    i.HC595_SendByte                         0x08001028   Section        0  freertos.o(i.HC595_SendByte)
    i.HardFault_Handler                      0x08001080   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.LedTimerCallback                       0x08001082   Section        0  freertos.o(i.LedTimerCallback)
    i.MX_FREERTOS_Init                       0x08001084   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x080011bc   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_IWDG_Init                           0x08001288   Section        0  iwdg.o(i.MX_IWDG_Init)
    i.MemManage_Handler                      0x080012b4   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080012b6   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.StartDemoControlTask                   0x080012b8   Section        0  freertos.o(i.StartDemoControlTask)
    i.StartDisplayTask                       0x080012f4   Section        0  freertos.o(i.StartDisplayTask)
    i.StartKeyTask                           0x08001314   Section        0  freertos.o(i.StartKeyTask)
    i.StartLedControlTask                    0x0800132c   Section        0  freertos.o(i.StartLedControlTask)
    i.StartMonitorTask                       0x08001348   Section        0  freertos.o(i.StartMonitorTask)
    i.SysTick_Handler                        0x08001364   Section        0  port.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001390   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080013f4   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM1_UP_IRQHandler                     0x080013f8   Section        0  stm32f1xx_it.o(i.TIM1_UP_IRQHandler)
    i.TIM_Base_SetConfig                     0x08001404   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.UsageFault_Handler                     0x080014a0   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.WatchdogTimerCallback                  0x080014a2   Section        0  freertos.o(i.WatchdogTimerCallback)
    i.__NVIC_SetPriority                     0x080014a4   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080014a5   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.inHandlerMode                          0x080014c4   Section        0  cmsis_os.o(i.inHandlerMode)
    inHandlerMode                            0x080014c5   Thumb Code    12  cmsis_os.o(i.inHandlerMode)
    i.main                                   0x080014d0   Section        0  main.o(i.main)
    i.makeFreeRtosPriority                   0x080014ea   Section        0  cmsis_os.o(i.makeFreeRtosPriority)
    makeFreeRtosPriority                     0x080014eb   Thumb Code    12  cmsis_os.o(i.makeFreeRtosPriority)
    i.osDelay                                0x080014f6   Section        0  cmsis_os.o(i.osDelay)
    i.osKernelStart                          0x08001504   Section        0  cmsis_os.o(i.osKernelStart)
    i.osMessageCreate                        0x0800150e   Section        0  cmsis_os.o(i.osMessageCreate)
    i.osMutexCreate                          0x08001534   Section        0  cmsis_os.o(i.osMutexCreate)
    i.osMutexRelease                         0x08001544   Section        0  cmsis_os.o(i.osMutexRelease)
    i.osMutexWait                            0x08001594   Section        0  cmsis_os.o(i.osMutexWait)
    i.osSemaphoreCreate                      0x080015f8   Section        0  cmsis_os.o(i.osSemaphoreCreate)
    i.osThreadCreate                         0x08001648   Section        0  cmsis_os.o(i.osThreadCreate)
    i.osTimerCreate                          0x0800169c   Section        0  cmsis_os.o(i.osTimerCreate)
    i.prvAddCurrentTaskToDelayedList         0x080016dc   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x080016dd   Thumb Code   102  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x0800174c   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x0800174d   Thumb Code   194  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x0800181c   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x0800181d   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCopyDataFromQueue                   0x08001874   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08001875   Thumb Code    38  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x0800189a   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x0800189b   Thumb Code   108  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x08001906   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08001907   Thumb Code    52  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x0800193c   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x0800193d   Thumb Code    66  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08001988   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08001989   Thumb Code    82  tasks.o(i.prvIdleTask)
    i.prvInitialiseMutex                     0x080019e8   Section        0  queue.o(i.prvInitialiseMutex)
    prvInitialiseMutex                       0x080019e9   Thumb Code    22  queue.o(i.prvInitialiseMutex)
    i.prvInitialiseNewQueue                  0x080019fe   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x080019ff   Thumb Code    22  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08001a14   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08001a15   Thumb Code   152  tasks.o(i.prvInitialiseNewTask)
    i.prvInitialiseNewTimer                  0x08001aac   Section        0  timers.o(i.prvInitialiseNewTimer)
    prvInitialiseNewTimer                    0x08001aad   Thumb Code    78  timers.o(i.prvInitialiseNewTimer)
    i.prvInsertBlockIntoFreeList             0x08001afc   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08001afd   Thumb Code    72  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x08001b48   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08001b49   Thumb Code    52  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08001b80   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08001b81   Thumb Code    28  queue.o(i.prvIsQueueEmpty)
    i.prvProcessReceivedCommands             0x08001b9c   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08001b9d   Thumb Code   234  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x08001c8c   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x08001c8d   Thumb Code   182  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x08001d4c   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08001d4d   Thumb Code    26  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08001d6c   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08001d6d   Thumb Code    36  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08001d94   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08001d95   Thumb Code   104  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08001e00   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08001e01   Thumb Code    36  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08001e28   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08001e29   Thumb Code    32  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08001e4c   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08001e4d   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08001eb8   Section        0  heap_4.o(i.pvPortMalloc)
    i.pvTaskIncrementMutexHeldCount          0x08001f94   Section        0  tasks.o(i.pvTaskIncrementMutexHeldCount)
    i.pxPortInitialiseStack                  0x08001fac   Section        0  port.o(i.pxPortInitialiseStack)
    i.uxListRemove                           0x08001fd0   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x08001ff8   Section        0  freertos.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x0800200c   Section        0  freertos.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x08002024   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x0800203a   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08002040   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08002070   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08002088   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x080020c8   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x080020f0   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08002154   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x08002178   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x080021cc   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueWaitForMessageRestricted         0x080021f4   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x08002238   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08002284   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08002294   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x080022a0   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x080022d0   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskPriorityDisinheritAfterTimeout    0x08002308   Section        0  tasks.o(i.vTaskPriorityDisinheritAfterTimeout)
    i.vTaskStartScheduler                    0x080023a8   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08002430   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08002440   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x0800249c   Section        0  port.o(i.xPortStartScheduler)
    i.xQueueCreateCountingSemaphore          0x08002544   Section        0  queue.o(i.xQueueCreateCountingSemaphore)
    i.xQueueCreateCountingSemaphoreStatic    0x0800257e   Section        0  queue.o(i.xQueueCreateCountingSemaphoreStatic)
    i.xQueueCreateMutex                      0x080025be   Section        0  queue.o(i.xQueueCreateMutex)
    i.xQueueCreateMutexStatic                0x080025d4   Section        0  queue.o(i.xQueueCreateMutexStatic)
    i.xQueueGenericCreate                    0x080025ee   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueGenericCreateStatic              0x08002630   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x08002698   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x08002720   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x08002880   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueGiveFromISR                      0x0800293e   Section        0  queue.o(i.xQueueGiveFromISR)
    i.xQueueReceive                          0x080029dc   Section        0  queue.o(i.xQueueReceive)
    i.xQueueReceiveFromISR                   0x08002b14   Section        0  queue.o(i.xQueueReceiveFromISR)
    i.xQueueSemaphoreTake                    0x08002bb0   Section        0  queue.o(i.xQueueSemaphoreTake)
    i.xTaskCheckForTimeOut                   0x08002d28   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08002d9c   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x08002df6   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetSchedulerState                 0x08002e4c   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08002e68   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x08002e74   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x08002f40   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskPriorityInherit                   0x08002fd8   Section        0  tasks.o(i.xTaskPriorityInherit)
    i.xTaskRemoveFromEventList               0x08003064   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x080030d8   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreate                           0x0800319c   Section        0  timers.o(i.xTimerCreate)
    i.xTimerCreateStatic                     0x080031d0   Section        0  timers.o(i.xTimerCreateStatic)
    i.xTimerCreateTimerTask                  0x08003200   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x08003260   Section        0  timers.o(i.xTimerGenericCommand)
    .constdata                               0x080032c0   Section      204  freertos.o(.constdata)
    .constdata                               0x0800338c   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x0800338c   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x0800338e   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x0800339e   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x080033ae   Section        8  system_stm32f1xx.o(.constdata)
    .conststring                             0x080033b8   Section       64  freertos.o(.conststring)
    .data                                    0x20000000   Section       90  freertos.o(.data)
    current_digit                            0x20000000   Data           1  freertos.o(.data)
    display_buffer                           0x20000040   Data           8  freertos.o(.data)
    digit_segments                           0x20000048   Data          18  freertos.o(.data)
    .data                                    0x2000005c   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000068   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x2000006c   Section       60  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000070   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x20000074   Data           4  tasks.o(.data)
    xTickCount                               0x20000078   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x2000007c   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000080   Data           4  tasks.o(.data)
    xPendedTicks                             0x20000084   Data           4  tasks.o(.data)
    xYieldPending                            0x20000088   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x2000008c   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000090   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x20000094   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000098   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x2000009c   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x200000a0   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x200000a4   Data           4  tasks.o(.data)
    .data                                    0x200000a8   Section       20  timers.o(.data)
    xTimerQueue                              0x200000a8   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x200000ac   Data           4  timers.o(.data)
    xLastTime                                0x200000b0   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x200000b4   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x200000b8   Data           4  timers.o(.data)
    .data                                    0x200000bc   Section       32  heap_4.o(.data)
    pxEnd                                    0x200000bc   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x200000c0   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x200000c4   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x200000c8   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x200000cc   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x200000d0   Data           4  heap_4.o(.data)
    xStart                                   0x200000d4   Data           8  heap_4.o(.data)
    .data                                    0x200000dc   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x200000dc   Data           1  port.o(.data)
    uxCriticalNesting                        0x200000e0   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x200000e4   Data           4  port.o(.data)
    .bss                                     0x200000e8   Section     1704  freertos.o(.bss)
    xIdleTaskTCBBuffer                       0x200000e8   Data          84  freertos.o(.bss)
    xIdleStack                               0x2000013c   Data         512  freertos.o(.bss)
    xTimerTaskTCBBuffer                      0x2000033c   Data          84  freertos.o(.bss)
    xTimerStack                              0x20000390   Data        1024  freertos.o(.bss)
    .bss                                     0x20000790   Section       12  iwdg.o(.bss)
    .bss                                     0x2000079c   Section       72  stm32f1xx_hal_timebase_tim.o(.bss)
    .bss                                     0x200007e4   Section       64  queue.o(.bss)
    .bss                                     0x20000824   Section      240  tasks.o(.bss)
    pxReadyTasksLists                        0x20000824   Data         140  tasks.o(.bss)
    xDelayedTaskList1                        0x200008b0   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x200008c4   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x200008d8   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x200008ec   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20000900   Data          20  tasks.o(.bss)
    .bss                                     0x20000914   Section      232  timers.o(.bss)
    xStaticTimerQueue                        0x20000914   Data          72  timers.o(.bss)
    ucStaticTimerQueueStorage                0x2000095c   Data         120  timers.o(.bss)
    xActiveTimerList1                        0x200009d4   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x200009e8   Data          20  timers.o(.bss)
    .bss                                     0x200009fc   Section     8192  heap_4.o(.bss)
    ucHeap                                   0x200009fc   Data        8192  heap_4.o(.bss)
    .bss                                     0x200029fc   Section       96  libspace.o(.bss)
    HEAP                                     0x20002a60   Section      512  startup_stm32f103xe.o(HEAP)
    Heap_Mem                                 0x20002a60   Data         512  startup_stm32f103xe.o(HEAP)
    STACK                                    0x20002c60   Section     1024  startup_stm32f103xe.o(STACK)
    Stack_Mem                                0x20002c60   Data        1024  startup_stm32f103xe.o(STACK)
    __initial_sp                             0x20003060   Data           0  startup_stm32f103xe.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f103xe.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xe.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f103xe.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x080001cd   Thumb Code    32  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001f1   Thumb Code    28  port.o(.emb_text)
    PendSV_Handler                           0x08000211   Thumb Code    72  port.o(.emb_text)
    vPortGetIPSR                             0x0800025d   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x08000265   Thumb Code     8  startup_stm32f103xe.o(.text)
    ADC1_2_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    ADC3_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_RX1_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_SCE_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI0_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI15_10_IRQHandler                     0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI1_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI2_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI3_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI4_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI9_5_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    FLASH_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    FSMC_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_ER_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_EV_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_ER_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_EV_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    PVD_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RCC_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_Alarm_IRQHandler                     0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_IRQHandler                           0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SDIO_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI1_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI2_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI3_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TAMPER_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_BRK_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_CC_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM2_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM3_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM4_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM5_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM6_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM7_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_BRK_IRQHandler                      0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_CC_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_UP_IRQHandler                       0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART4_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART5_IRQHandler                         0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART1_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART2_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART3_IRQHandler                        0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USBWakeUp_IRQHandler                     0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    WWDG_IRQHandler                          0x0800027f   Thumb Code     0  startup_stm32f103xe.o(.text)
    __user_initial_stackheap                 0x08000281   Thumb Code     0  startup_stm32f103xe.o(.text)
    __aeabi_memcpy                           0x080002a5   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x080002a5   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800030b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x0800032f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800032f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800032f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000377   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x08000393   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000393   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000393   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000397   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080003e1   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080003e3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080003e5   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x080003e7   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000431   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000445   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000445   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000445   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x0800044d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000459   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000459   Thumb Code     2  use_no_semi.o(.text)
    BeepTimerCallback                        0x0800045b   Thumb Code     2  freertos.o(i.BeepTimerCallback)
    __semihosting_library_function           0x0800045b   Thumb Code     0  indicate_semi.o(.text)
    BusFault_Handler                         0x0800045d   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x0800045f   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    DisplayRefreshCallback                   0x08000461   Thumb Code     4  freertos.o(i.DisplayRefreshCallback)
    Display_Clear                            0x08000465   Thumb Code    42  freertos.o(i.Display_Clear)
    Display_Refresh                          0x08000495   Thumb Code    72  freertos.o(i.Display_Refresh)
    Error_Handler                            0x080004e1   Thumb Code     4  main.o(i.Error_Handler)
    HAL_GPIO_Init                            0x080004e5   Thumb Code   462  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x080006dd   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x080006ed   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080006f9   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IWDG_Init                            0x08000705   Thumb Code    94  stm32f1xx_hal_iwdg.o(i.HAL_IWDG_Init)
    HAL_IncTick                              0x08000765   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000775   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000799   Thumb Code   118  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000825   Thumb Code    48  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000859   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000875   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080008b5   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080008d9   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x08000a05   Thumb Code    54  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetPCLK2Freq                     0x08000a45   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000a65   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000ab1   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_TIMEx_BreakCallback                  0x08000dd1   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08000dd3   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_Base_Init                        0x08000dd5   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08000e2f   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08000e31   Thumb Code    88  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x08000e9d   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08000e9f   Thumb Code   304  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x08000fcf   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_PulseFinishedCallback        0x08000fd1   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PeriodElapsedCallback            0x08000fd5   Thumb Code    14  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08000fe9   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HC138_SelectDigit                        0x08000fed   Thumb Code    54  freertos.o(i.HC138_SelectDigit)
    HC595_SendByte                           0x08001029   Thumb Code    82  freertos.o(i.HC595_SendByte)
    HardFault_Handler                        0x08001081   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    LedTimerCallback                         0x08001083   Thumb Code     2  freertos.o(i.LedTimerCallback)
    MX_FREERTOS_Init                         0x08001085   Thumb Code   304  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x080011bd   Thumb Code   186  gpio.o(i.MX_GPIO_Init)
    MX_IWDG_Init                             0x08001289   Thumb Code    36  iwdg.o(i.MX_IWDG_Init)
    MemManage_Handler                        0x080012b5   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080012b7   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    StartDemoControlTask                     0x080012b9   Thumb Code    54  freertos.o(i.StartDemoControlTask)
    StartDisplayTask                         0x080012f5   Thumb Code    26  freertos.o(i.StartDisplayTask)
    StartKeyTask                             0x08001315   Thumb Code    18  freertos.o(i.StartKeyTask)
    StartLedControlTask                      0x0800132d   Thumb Code    22  freertos.o(i.StartLedControlTask)
    StartMonitorTask                         0x08001349   Thumb Code    22  freertos.o(i.StartMonitorTask)
    SysTick_Handler                          0x08001365   Thumb Code    38  port.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001391   Thumb Code   100  main.o(i.SystemClock_Config)
    SystemInit                               0x080013f5   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM1_UP_IRQHandler                       0x080013f9   Thumb Code     6  stm32f1xx_it.o(i.TIM1_UP_IRQHandler)
    TIM_Base_SetConfig                       0x08001405   Thumb Code   134  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    UsageFault_Handler                       0x080014a1   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    WatchdogTimerCallback                    0x080014a3   Thumb Code     2  freertos.o(i.WatchdogTimerCallback)
    main                                     0x080014d1   Thumb Code    26  main.o(i.main)
    osDelay                                  0x080014f7   Thumb Code    14  cmsis_os.o(i.osDelay)
    osKernelStart                            0x08001505   Thumb Code    10  cmsis_os.o(i.osKernelStart)
    osMessageCreate                          0x0800150f   Thumb Code    38  cmsis_os.o(i.osMessageCreate)
    osMutexCreate                            0x08001535   Thumb Code    16  cmsis_os.o(i.osMutexCreate)
    osMutexRelease                           0x08001545   Thumb Code    74  cmsis_os.o(i.osMutexRelease)
    osMutexWait                              0x08001595   Thumb Code    96  cmsis_os.o(i.osMutexWait)
    osSemaphoreCreate                        0x080015f9   Thumb Code    80  cmsis_os.o(i.osSemaphoreCreate)
    osThreadCreate                           0x08001649   Thumb Code    84  cmsis_os.o(i.osThreadCreate)
    osTimerCreate                            0x0800169d   Thumb Code    60  cmsis_os.o(i.osTimerCreate)
    pvPortMalloc                             0x08001eb9   Thumb Code   216  heap_4.o(i.pvPortMalloc)
    pvTaskIncrementMutexHeldCount            0x08001f95   Thumb Code    18  tasks.o(i.pvTaskIncrementMutexHeldCount)
    pxPortInitialiseStack                    0x08001fad   Thumb Code    32  port.o(i.pxPortInitialiseStack)
    uxListRemove                             0x08001fd1   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x08001ff9   Thumb Code    16  freertos.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x0800200d   Thumb Code    18  freertos.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x08002025   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x0800203b   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08002041   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x08002071   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08002089   Thumb Code    54  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x080020c9   Thumb Code    34  port.o(i.vPortExitCritical)
    vPortFree                                0x080020f1   Thumb Code    94  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08002155   Thumb Code    32  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x08002179   Thumb Code    74  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x080021cd   Thumb Code    34  queue.o(i.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x080021f5   Thumb Code    68  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x08002239   Thumb Code    66  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08002285   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08002295   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x080022a1   Thumb Code    44  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x080022d1   Thumb Code    52  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskPriorityDisinheritAfterTimeout      0x08002309   Thumb Code   152  tasks.o(i.vTaskPriorityDisinheritAfterTimeout)
    vTaskStartScheduler                      0x080023a9   Thumb Code   118  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08002431   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08002441   Thumb Code    82  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x0800249d   Thumb Code   154  port.o(i.xPortStartScheduler)
    xQueueCreateCountingSemaphore            0x08002545   Thumb Code    58  queue.o(i.xQueueCreateCountingSemaphore)
    xQueueCreateCountingSemaphoreStatic      0x0800257f   Thumb Code    64  queue.o(i.xQueueCreateCountingSemaphoreStatic)
    xQueueCreateMutex                        0x080025bf   Thumb Code    22  queue.o(i.xQueueCreateMutex)
    xQueueCreateMutexStatic                  0x080025d5   Thumb Code    26  queue.o(i.xQueueCreateMutexStatic)
    xQueueGenericCreate                      0x080025ef   Thumb Code    66  queue.o(i.xQueueGenericCreate)
    xQueueGenericCreateStatic                0x08002631   Thumb Code   102  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x08002699   Thumb Code   132  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x08002721   Thumb Code   346  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x08002881   Thumb Code   190  queue.o(i.xQueueGenericSendFromISR)
    xQueueGiveFromISR                        0x0800293f   Thumb Code   156  queue.o(i.xQueueGiveFromISR)
    xQueueReceive                            0x080029dd   Thumb Code   308  queue.o(i.xQueueReceive)
    xQueueReceiveFromISR                     0x08002b15   Thumb Code   154  queue.o(i.xQueueReceiveFromISR)
    xQueueSemaphoreTake                      0x08002bb1   Thumb Code   372  queue.o(i.xQueueSemaphoreTake)
    xTaskCheckForTimeOut                     0x08002d29   Thumb Code   112  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08002d9d   Thumb Code    90  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x08002df7   Thumb Code    86  tasks.o(i.xTaskCreateStatic)
    xTaskGetSchedulerState                   0x08002e4d   Thumb Code    22  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08002e69   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x08002e75   Thumb Code   194  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x08002f41   Thumb Code   144  tasks.o(i.xTaskPriorityDisinherit)
    xTaskPriorityInherit                     0x08002fd9   Thumb Code   130  tasks.o(i.xTaskPriorityInherit)
    xTaskRemoveFromEventList                 0x08003065   Thumb Code   108  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x080030d9   Thumb Code   182  tasks.o(i.xTaskResumeAll)
    xTimerCreate                             0x0800319d   Thumb Code    52  timers.o(i.xTimerCreate)
    xTimerCreateStatic                       0x080031d1   Thumb Code    46  timers.o(i.xTimerCreateStatic)
    xTimerCreateTimerTask                    0x08003201   Thumb Code    78  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x08003261   Thumb Code    92  timers.o(i.xTimerGenericCommand)
    AHBPrescTable                            0x0800339e   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x080033ae   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x080033f8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003418   Number         0  anon$$obj.o(Region$$Table)
    MonitorTaskHandle                        0x20000004   Data           4  freertos.o(.data)
    DisplayTaskHandle                        0x20000008   Data           4  freertos.o(.data)
    LedControlTaskHandle                     0x2000000c   Data           4  freertos.o(.data)
    KeyTaskHandle                            0x20000010   Data           4  freertos.o(.data)
    DemoControlTaskHandle                    0x20000014   Data           4  freertos.o(.data)
    KeyEventQueueHandle                      0x20000018   Data           4  freertos.o(.data)
    DisplayDataQueueHandle                   0x2000001c   Data           4  freertos.o(.data)
    LedTimerHandle                           0x20000020   Data           4  freertos.o(.data)
    BeepTimerHandle                          0x20000024   Data           4  freertos.o(.data)
    WatchdogTimerHandle                      0x20000028   Data           4  freertos.o(.data)
    DisplayRefreshTimerHandle                0x2000002c   Data           4  freertos.o(.data)
    DisplayMutexHandle                       0x20000030   Data           4  freertos.o(.data)
    SystemMutexHandle                        0x20000034   Data           4  freertos.o(.data)
    KeySemaphoreHandle                       0x20000038   Data           4  freertos.o(.data)
    ResourceSemaphoreHandle                  0x2000003c   Data           4  freertos.o(.data)
    uwTickFreq                               0x2000005c   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000060   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000064   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x20000068   Data           4  system_stm32f1xx.o(.data)
    pxCurrentTCB                             0x2000006c   Data           4  tasks.o(.data)
    hiwdg                                    0x20000790   Data          12  iwdg.o(.bss)
    htim1                                    0x2000079c   Data          72  stm32f1xx_hal_timebase_tim.o(.bss)
    xQueueRegistry                           0x200007e4   Data          64  queue.o(.bss)
    __libspace_start                         0x200029fc   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20002a5c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003500, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003418, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO            3    RESET               startup_stm32f103xe.o
    0x08000130   0x08000130   0x00000008   Code   RO         4034  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         4195    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO         4197    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO         4199    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000002   Code   RO         4063    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4070    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4072    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4075    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4077    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4079    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4082    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4084    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4086    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4088    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4090    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4092    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4094    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4096    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4098    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4100    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4102    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4106    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4108    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4110    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000000   Code   RO         4112    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x080001a6   0x00000002   Code   RO         4113    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x080001a8   0x00000002   Code   RO         4133    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4146    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4148    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4150    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4153    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4156    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4158    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000000   Code   RO         4161    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080001aa   0x080001aa   0x00000002   Code   RO         4162    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         4036    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x080001ac   0x00000000   Code   RO         4040    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x080001ac   0x00000006   Code   RO         4052    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x080001b2   0x00000000   Code   RO         4042    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x080001b2   0x00000004   Code   RO         4043    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO         4045    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000008   Code   RO         4046    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x080001be   0x00000002   Code   RO         4067    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         4115    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         4116    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x080001c4   0x00000006   Code   RO         4117    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000002   PAD
    0x080001cc   0x080001cc   0x00000096   Code   RO         3941    .emb_text           port.o
    0x08000262   0x08000262   0x00000002   PAD
    0x08000264   0x08000264   0x00000040   Code   RO            4    .text               startup_stm32f103xe.o
    0x080002a4   0x080002a4   0x0000008a   Code   RO         4024    .text               c_w.l(rt_memcpy_v6.o)
    0x0800032e   0x0800032e   0x00000064   Code   RO         4026    .text               c_w.l(rt_memcpy_w.o)
    0x08000392   0x08000392   0x0000004e   Code   RO         4030    .text               c_w.l(rt_memclr_w.o)
    0x080003e0   0x080003e0   0x00000006   Code   RO         4032    .text               c_w.l(heapauxi.o)
    0x080003e6   0x080003e6   0x0000004a   Code   RO         4054    .text               c_w.l(sys_stackheap_outer.o)
    0x08000430   0x08000430   0x00000012   Code   RO         4056    .text               c_w.l(exit.o)
    0x08000442   0x08000442   0x00000002   PAD
    0x08000444   0x08000444   0x00000008   Code   RO         4064    .text               c_w.l(libspace.o)
    0x0800044c   0x0800044c   0x0000000c   Code   RO         4125    .text               c_w.l(sys_exit.o)
    0x08000458   0x08000458   0x00000002   Code   RO         4136    .text               c_w.l(use_no_semi.o)
    0x0800045a   0x0800045a   0x00000000   Code   RO         4138    .text               c_w.l(indicate_semi.o)
    0x0800045a   0x0800045a   0x00000002   Code   RO          216    i.BeepTimerCallback  freertos.o
    0x0800045c   0x0800045c   0x00000002   Code   RO          395    i.BusFault_Handler  stm32f1xx_it.o
    0x0800045e   0x0800045e   0x00000002   Code   RO          396    i.DebugMon_Handler  stm32f1xx_it.o
    0x08000460   0x08000460   0x00000004   Code   RO          217    i.DisplayRefreshCallback  freertos.o
    0x08000464   0x08000464   0x00000030   Code   RO          218    i.Display_Clear     freertos.o
    0x08000494   0x08000494   0x0000004c   Code   RO          219    i.Display_Refresh   freertos.o
    0x080004e0   0x080004e0   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080004e4   0x080004e4   0x000001f8   Code   RO          875    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080006dc   0x080006dc   0x00000010   Code   RO          878    i.HAL_GPIO_TogglePin  stm32f1xx_hal_gpio.o
    0x080006ec   0x080006ec   0x0000000a   Code   RO          879    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x080006f6   0x080006f6   0x00000002   PAD
    0x080006f8   0x080006f8   0x0000000c   Code   RO          569    i.HAL_GetTick       stm32f1xx_hal.o
    0x08000704   0x08000704   0x0000005e   Code   RO         1535    i.HAL_IWDG_Init     stm32f1xx_hal_iwdg.o
    0x08000762   0x08000762   0x00000002   PAD
    0x08000764   0x08000764   0x00000010   Code   RO          575    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000774   0x08000774   0x00000024   Code   RO          576    i.HAL_Init          stm32f1xx_hal.o
    0x08000798   0x08000798   0x0000008c   Code   RO          483    i.HAL_InitTick      stm32f1xx_hal_timebase_tim.o
    0x08000824   0x08000824   0x00000034   Code   RO          459    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000858   0x08000858   0x0000001a   Code   RO         1035    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08000872   0x08000872   0x00000002   PAD
    0x08000874   0x08000874   0x00000040   Code   RO         1041    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080008b4   0x080008b4   0x00000024   Code   RO         1042    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x080008d8   0x080008d8   0x0000012c   Code   RO          733    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08000a04   0x08000a04   0x00000040   Code   RO          737    i.HAL_RCC_GetClockConfig  stm32f1xx_hal_rcc.o
    0x08000a44   0x08000a44   0x00000020   Code   RO          741    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08000a64   0x08000a64   0x0000004c   Code   RO          742    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08000ab0   0x08000ab0   0x00000320   Code   RO          745    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08000dd0   0x08000dd0   0x00000002   Code   RO         2271    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08000dd2   0x08000dd2   0x00000002   Code   RO         2272    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08000dd4   0x08000dd4   0x0000005a   Code   RO         1567    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08000e2e   0x08000e2e   0x00000002   Code   RO         1569    i.HAL_TIM_Base_MspInit  stm32f1xx_hal_tim.o
    0x08000e30   0x08000e30   0x0000006c   Code   RO         1572    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08000e9c   0x08000e9c   0x00000002   Code   RO         1601    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08000e9e   0x08000e9e   0x00000130   Code   RO         1615    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08000fce   0x08000fce   0x00000002   Code   RO         1618    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08000fd0   0x08000fd0   0x00000002   Code   RO         1645    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x08000fd2   0x08000fd2   0x00000002   PAD
    0x08000fd4   0x08000fd4   0x00000014   Code   RO           14    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x08000fe8   0x08000fe8   0x00000002   Code   RO         1658    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x08000fea   0x08000fea   0x00000002   PAD
    0x08000fec   0x08000fec   0x0000003c   Code   RO          223    i.HC138_SelectDigit  freertos.o
    0x08001028   0x08001028   0x00000058   Code   RO          224    i.HC595_SendByte    freertos.o
    0x08001080   0x08001080   0x00000002   Code   RO          397    i.HardFault_Handler  stm32f1xx_it.o
    0x08001082   0x08001082   0x00000002   Code   RO          225    i.LedTimerCallback  freertos.o
    0x08001084   0x08001084   0x00000138   Code   RO          226    i.MX_FREERTOS_Init  freertos.o
    0x080011bc   0x080011bc   0x000000cc   Code   RO          192    i.MX_GPIO_Init      gpio.o
    0x08001288   0x08001288   0x0000002c   Code   RO          365    i.MX_IWDG_Init      iwdg.o
    0x080012b4   0x080012b4   0x00000002   Code   RO          398    i.MemManage_Handler  stm32f1xx_it.o
    0x080012b6   0x080012b6   0x00000002   Code   RO          399    i.NMI_Handler       stm32f1xx_it.o
    0x080012b8   0x080012b8   0x0000003c   Code   RO          227    i.StartDemoControlTask  freertos.o
    0x080012f4   0x080012f4   0x00000020   Code   RO          228    i.StartDisplayTask  freertos.o
    0x08001314   0x08001314   0x00000018   Code   RO          229    i.StartKeyTask      freertos.o
    0x0800132c   0x0800132c   0x0000001c   Code   RO          230    i.StartLedControlTask  freertos.o
    0x08001348   0x08001348   0x0000001c   Code   RO          231    i.StartMonitorTask  freertos.o
    0x08001364   0x08001364   0x0000002c   Code   RO         3942    i.SysTick_Handler   port.o
    0x08001390   0x08001390   0x00000064   Code   RO           15    i.SystemClock_Config  main.o
    0x080013f4   0x080013f4   0x00000002   Code   RO         2539    i.SystemInit        system_stm32f1xx.o
    0x080013f6   0x080013f6   0x00000002   PAD
    0x080013f8   0x080013f8   0x0000000c   Code   RO          400    i.TIM1_UP_IRQHandler  stm32f1xx_it.o
    0x08001404   0x08001404   0x0000009c   Code   RO         1660    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x080014a0   0x080014a0   0x00000002   Code   RO          401    i.UsageFault_Handler  stm32f1xx_it.o
    0x080014a2   0x080014a2   0x00000002   Code   RO          232    i.WatchdogTimerCallback  freertos.o
    0x080014a4   0x080014a4   0x00000020   Code   RO         1048    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080014c4   0x080014c4   0x0000000c   Code   RO         3517    i.inHandlerMode     cmsis_os.o
    0x080014d0   0x080014d0   0x0000001a   Code   RO           16    i.main              main.o
    0x080014ea   0x080014ea   0x0000000c   Code   RO         3519    i.makeFreeRtosPriority  cmsis_os.o
    0x080014f6   0x080014f6   0x0000000e   Code   RO         3521    i.osDelay           cmsis_os.o
    0x08001504   0x08001504   0x0000000a   Code   RO         3524    i.osKernelStart     cmsis_os.o
    0x0800150e   0x0800150e   0x00000026   Code   RO         3533    i.osMessageCreate   cmsis_os.o
    0x08001534   0x08001534   0x00000010   Code   RO         3539    i.osMutexCreate     cmsis_os.o
    0x08001544   0x08001544   0x00000050   Code   RO         3541    i.osMutexRelease    cmsis_os.o
    0x08001594   0x08001594   0x00000064   Code   RO         3542    i.osMutexWait       cmsis_os.o
    0x080015f8   0x080015f8   0x00000050   Code   RO         3550    i.osSemaphoreCreate  cmsis_os.o
    0x08001648   0x08001648   0x00000054   Code   RO         3558    i.osThreadCreate    cmsis_os.o
    0x0800169c   0x0800169c   0x00000040   Code   RO         3569    i.osTimerCreate     cmsis_os.o
    0x080016dc   0x080016dc   0x00000070   Code   RO         3059    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x0800174c   0x0800174c   0x000000d0   Code   RO         3060    i.prvAddNewTaskToReadyList  tasks.o
    0x0800181c   0x0800181c   0x00000058   Code   RO         3373    i.prvCheckForValidListAndQueue  timers.o
    0x08001874   0x08001874   0x00000026   Code   RO         2717    i.prvCopyDataFromQueue  queue.o
    0x0800189a   0x0800189a   0x0000006c   Code   RO         2718    i.prvCopyDataToQueue  queue.o
    0x08001906   0x08001906   0x00000034   Code   RO         3061    i.prvDeleteTCB      tasks.o
    0x0800193a   0x0800193a   0x00000002   PAD
    0x0800193c   0x0800193c   0x0000004c   Code   RO         3886    i.prvHeapInit       heap_4.o
    0x08001988   0x08001988   0x00000060   Code   RO         3062    i.prvIdleTask       tasks.o
    0x080019e8   0x080019e8   0x00000016   Code   RO         2719    i.prvInitialiseMutex  queue.o
    0x080019fe   0x080019fe   0x00000016   Code   RO         2720    i.prvInitialiseNewQueue  queue.o
    0x08001a14   0x08001a14   0x00000098   Code   RO         3063    i.prvInitialiseNewTask  tasks.o
    0x08001aac   0x08001aac   0x0000004e   Code   RO         3374    i.prvInitialiseNewTimer  timers.o
    0x08001afa   0x08001afa   0x00000002   PAD
    0x08001afc   0x08001afc   0x0000004c   Code   RO         3887    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08001b48   0x08001b48   0x00000038   Code   RO         3375    i.prvInsertTimerInActiveList  timers.o
    0x08001b80   0x08001b80   0x0000001c   Code   RO         2721    i.prvIsQueueEmpty   queue.o
    0x08001b9c   0x08001b9c   0x000000f0   Code   RO         3376    i.prvProcessReceivedCommands  timers.o
    0x08001c8c   0x08001c8c   0x000000c0   Code   RO         3377    i.prvProcessTimerOrBlockTask  timers.o
    0x08001d4c   0x08001d4c   0x00000020   Code   RO         3064    i.prvResetNextTaskUnblockTime  tasks.o
    0x08001d6c   0x08001d6c   0x00000028   Code   RO         3378    i.prvSampleTimeNow  timers.o
    0x08001d94   0x08001d94   0x0000006c   Code   RO         3379    i.prvSwitchTimerLists  timers.o
    0x08001e00   0x08001e00   0x00000028   Code   RO         3943    i.prvTaskExitError  port.o
    0x08001e28   0x08001e28   0x00000024   Code   RO         3380    i.prvTimerTask      timers.o
    0x08001e4c   0x08001e4c   0x0000006a   Code   RO         2722    i.prvUnlockQueue    queue.o
    0x08001eb6   0x08001eb6   0x00000002   PAD
    0x08001eb8   0x08001eb8   0x000000dc   Code   RO         3888    i.pvPortMalloc      heap_4.o
    0x08001f94   0x08001f94   0x00000018   Code   RO         3066    i.pvTaskIncrementMutexHeldCount  tasks.o
    0x08001fac   0x08001fac   0x00000024   Code   RO         3944    i.pxPortInitialiseStack  port.o
    0x08001fd0   0x08001fd0   0x00000026   Code   RO         2677    i.uxListRemove      list.o
    0x08001ff6   0x08001ff6   0x00000002   PAD
    0x08001ff8   0x08001ff8   0x00000014   Code   RO          233    i.vApplicationGetIdleTaskMemory  freertos.o
    0x0800200c   0x0800200c   0x00000018   Code   RO          234    i.vApplicationGetTimerTaskMemory  freertos.o
    0x08002024   0x08002024   0x00000016   Code   RO         2678    i.vListInitialise   list.o
    0x0800203a   0x0800203a   0x00000006   Code   RO         2679    i.vListInitialiseItem  list.o
    0x08002040   0x08002040   0x00000030   Code   RO         2680    i.vListInsert       list.o
    0x08002070   0x08002070   0x00000018   Code   RO         2681    i.vListInsertEnd    list.o
    0x08002088   0x08002088   0x00000040   Code   RO         3946    i.vPortEnterCritical  port.o
    0x080020c8   0x080020c8   0x00000028   Code   RO         3947    i.vPortExitCritical  port.o
    0x080020f0   0x080020f0   0x00000064   Code   RO         3889    i.vPortFree         heap_4.o
    0x08002154   0x08002154   0x00000024   Code   RO         3948    i.vPortSetupTimerInterrupt  port.o
    0x08002178   0x08002178   0x00000054   Code   RO         3949    i.vPortValidateInterruptPriority  port.o
    0x080021cc   0x080021cc   0x00000028   Code   RO         2726    i.vQueueAddToRegistry  queue.o
    0x080021f4   0x080021f4   0x00000044   Code   RO         2729    i.vQueueWaitForMessageRestricted  queue.o
    0x08002238   0x08002238   0x0000004c   Code   RO         3073    i.vTaskDelay        tasks.o
    0x08002284   0x08002284   0x00000010   Code   RO         3076    i.vTaskInternalSetTimeOutState  tasks.o
    0x08002294   0x08002294   0x0000000c   Code   RO         3077    i.vTaskMissedYield  tasks.o
    0x080022a0   0x080022a0   0x00000030   Code   RO         3079    i.vTaskPlaceOnEventList  tasks.o
    0x080022d0   0x080022d0   0x00000038   Code   RO         3080    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08002308   0x08002308   0x000000a0   Code   RO         3082    i.vTaskPriorityDisinheritAfterTimeout  tasks.o
    0x080023a8   0x080023a8   0x00000088   Code   RO         3087    i.vTaskStartScheduler  tasks.o
    0x08002430   0x08002430   0x00000010   Code   RO         3089    i.vTaskSuspendAll   tasks.o
    0x08002440   0x08002440   0x0000005c   Code   RO         3090    i.vTaskSwitchContext  tasks.o
    0x0800249c   0x0800249c   0x000000a8   Code   RO         3950    i.xPortStartScheduler  port.o
    0x08002544   0x08002544   0x0000003a   Code   RO         2730    i.xQueueCreateCountingSemaphore  queue.o
    0x0800257e   0x0800257e   0x00000040   Code   RO         2731    i.xQueueCreateCountingSemaphoreStatic  queue.o
    0x080025be   0x080025be   0x00000016   Code   RO         2732    i.xQueueCreateMutex  queue.o
    0x080025d4   0x080025d4   0x0000001a   Code   RO         2733    i.xQueueCreateMutexStatic  queue.o
    0x080025ee   0x080025ee   0x00000042   Code   RO         2734    i.xQueueGenericCreate  queue.o
    0x08002630   0x08002630   0x00000066   Code   RO         2735    i.xQueueGenericCreateStatic  queue.o
    0x08002696   0x08002696   0x00000002   PAD
    0x08002698   0x08002698   0x00000088   Code   RO         2736    i.xQueueGenericReset  queue.o
    0x08002720   0x08002720   0x00000160   Code   RO         2737    i.xQueueGenericSend  queue.o
    0x08002880   0x08002880   0x000000be   Code   RO         2738    i.xQueueGenericSendFromISR  queue.o
    0x0800293e   0x0800293e   0x0000009c   Code   RO         2739    i.xQueueGiveFromISR  queue.o
    0x080029da   0x080029da   0x00000002   PAD
    0x080029dc   0x080029dc   0x00000138   Code   RO         2744    i.xQueueReceive     queue.o
    0x08002b14   0x08002b14   0x0000009a   Code   RO         2745    i.xQueueReceiveFromISR  queue.o
    0x08002bae   0x08002bae   0x00000002   PAD
    0x08002bb0   0x08002bb0   0x00000178   Code   RO         2746    i.xQueueSemaphoreTake  queue.o
    0x08002d28   0x08002d28   0x00000074   Code   RO         3092    i.xTaskCheckForTimeOut  tasks.o
    0x08002d9c   0x08002d9c   0x0000005a   Code   RO         3093    i.xTaskCreate       tasks.o
    0x08002df6   0x08002df6   0x00000056   Code   RO         3094    i.xTaskCreateStatic  tasks.o
    0x08002e4c   0x08002e4c   0x0000001c   Code   RO         3098    i.xTaskGetSchedulerState  tasks.o
    0x08002e68   0x08002e68   0x0000000c   Code   RO         3099    i.xTaskGetTickCount  tasks.o
    0x08002e74   0x08002e74   0x000000cc   Code   RO         3101    i.xTaskIncrementTick  tasks.o
    0x08002f40   0x08002f40   0x00000098   Code   RO         3104    i.xTaskPriorityDisinherit  tasks.o
    0x08002fd8   0x08002fd8   0x0000008c   Code   RO         3105    i.xTaskPriorityInherit  tasks.o
    0x08003064   0x08003064   0x00000074   Code   RO         3106    i.xTaskRemoveFromEventList  tasks.o
    0x080030d8   0x080030d8   0x000000c4   Code   RO         3107    i.xTaskResumeAll    tasks.o
    0x0800319c   0x0800319c   0x00000034   Code   RO         3385    i.xTimerCreate      timers.o
    0x080031d0   0x080031d0   0x0000002e   Code   RO         3386    i.xTimerCreateStatic  timers.o
    0x080031fe   0x080031fe   0x00000002   PAD
    0x08003200   0x08003200   0x00000060   Code   RO         3387    i.xTimerCreateTimerTask  timers.o
    0x08003260   0x08003260   0x00000060   Code   RO         3388    i.xTimerGenericCommand  timers.o
    0x080032c0   0x080032c0   0x000000cc   Data   RO          236    .constdata          freertos.o
    0x0800338c   0x0800338c   0x00000012   Data   RO          746    .constdata          stm32f1xx_hal_rcc.o
    0x0800339e   0x0800339e   0x00000010   Data   RO         2540    .constdata          system_stm32f1xx.o
    0x080033ae   0x080033ae   0x00000008   Data   RO         2541    .constdata          system_stm32f1xx.o
    0x080033b6   0x080033b6   0x00000002   PAD
    0x080033b8   0x080033b8   0x00000040   Data   RO          237    .conststring        freertos.o
    0x080033f8   0x080033f8   0x00000020   Data   RO         4193    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003418, Size: 0x00003060, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003418   0x0000005a   Data   RW          238    .data               freertos.o
    0x2000005a   0x08003472   0x00000002   PAD
    0x2000005c   0x08003474   0x0000000c   Data   RW          583    .data               stm32f1xx_hal.o
    0x20000068   0x08003480   0x00000004   Data   RW         2542    .data               system_stm32f1xx.o
    0x2000006c   0x08003484   0x0000003c   Data   RW         3110    .data               tasks.o
    0x200000a8   0x080034c0   0x00000014   Data   RW         3394    .data               timers.o
    0x200000bc   0x080034d4   0x00000020   Data   RW         3895    .data               heap_4.o
    0x200000dc   0x080034f4   0x0000000c   Data   RW         3951    .data               port.o
    0x200000e8        -       0x000006a8   Zero   RW          235    .bss                freertos.o
    0x20000790        -       0x0000000c   Zero   RW          366    .bss                iwdg.o
    0x2000079c        -       0x00000048   Zero   RW          486    .bss                stm32f1xx_hal_timebase_tim.o
    0x200007e4        -       0x00000040   Zero   RW         2747    .bss                queue.o
    0x20000824        -       0x000000f0   Zero   RW         3109    .bss                tasks.o
    0x20000914        -       0x000000e8   Zero   RW         3393    .bss                timers.o
    0x200009fc        -       0x00002000   Zero   RW         3894    .bss                heap_4.o
    0x200029fc        -       0x00000060   Zero   RW         4065    .bss                c_w.l(libspace.o)
    0x20002a5c   0x08003500   0x00000004   PAD
    0x20002a60        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xe.o
    0x20002c60        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xe.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       510         14          0          0          0      31646   cmsis_os.o
         0          0          0          0          0      20024   event_groups.o
       810         70        268         90       1704      10896   freertos.o
       204         18          0          0          0        923   gpio.o
       472         24          0         32       8192       4691   heap_4.o
        44          8          0          0         12        683   iwdg.o
       138          0          0          0          0       3318   list.o
       150          6          0          0          0     513308   main.o
       662         70          0         12          0      10933   port.o
      2446         24          0          0         64      25927   queue.o
        64         26        304          0       1536        800   startup_stm32f103xe.o
        64         14          0         12          0       5174   stm32f1xx_hal.o
       158         14          0          0          0      28118   stm32f1xx_hal_cortex.o
       530         42          0          0          0       3473   stm32f1xx_hal_gpio.o
        94          0          0          0          0       1272   stm32f1xx_hal_iwdg.o
        52          4          0          0          0        822   stm32f1xx_hal_msp.o
      1272         82         18          0          0       5102   stm32f1xx_hal_rcc.o
       668         42          0          0          0       7084   stm32f1xx_hal_tim.o
         4          0          0          0          0       1545   stm32f1xx_hal_tim_ex.o
       140         22          0          0         72       1387   stm32f1xx_hal_timebase_tim.o
        24          6          0          0          0       2938   stm32f1xx_it.o
         0          0          0          0          0        392   stream_buffer.o
         2          0         24          4          0       1083   system_stm32f1xx.o
      2428        184          0         60        240      28153   tasks.o
      1128         80          0         20        232      32523   timers.o

    ----------------------------------------------------------------------
     12094        <USER>        <GROUP>        232      12052     742215   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        30          0          2          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       594         <USER>          <GROUP>          0        100        812   Library Totals
         6          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       588         16          0          0         96        812   c_w.l

    ----------------------------------------------------------------------
       594         <USER>          <GROUP>          0        100        812   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12688        766        648        232      12152     735327   Grand Totals
     12688        766        648        232      12152     735327   ELF Image Totals
     12688        766        648        232          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13336 (  13.02kB)
    Total RW  Size (RW Data + ZI Data)             12384 (  12.09kB)
    Total ROM Size (Code + RO Data + RW Data)      13568 (  13.25kB)

==============================================================================

