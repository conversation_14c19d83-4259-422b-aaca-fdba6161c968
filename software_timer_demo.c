/**
 * @file software_timer_demo.c
 * @brief FreeRTOS软件定时器演示代码
 * <AUTHOR>
 * @date 2025-08-03
 */

#include "main.h"
#include "cmsis_os.h"
#include "FreeRTOS.h"
#include "timers.h"

/* 定时器句柄 */
TimerHandle_t xLedTimer;        // LED闪烁定时器
TimerHandle_t xWatchdogTimer;   // 看门狗定时器
TimerHandle_t xDataTimer;       // 数据采集定时器

/* 定时器ID (可选，用于区分不同定时器) */
#define LED_TIMER_ID        1
#define WATCHDOG_TIMER_ID   2
#define DATA_TIMER_ID       3

/* 外部变量声明 */
extern IWDG_HandleTypeDef hiwdg;

/**
 * @brief LED闪烁定时器回调函数
 * @param xTimer 定时器句柄
 */
void vLedTimerCallback(TimerHandle_t xTimer)
{
    /* 获取定时器ID */
    uint32_t ulTimerID = (uint32_t)pvTimerGetTimerID(xTimer);
    
    if (ulTimerID == LED_TIMER_ID) {
        /* 切换LED状态 */
        HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
        
        /* 可选：打印调试信息 */
        printf("LED Toggle at tick: %lu\r\n", xTaskGetTickCount());
    }
}

/**
 * @brief 看门狗定时器回调函数
 * @param xTimer 定时器句柄
 */
void vWatchdogTimerCallback(TimerHandle_t xTimer)
{
    uint32_t ulTimerID = (uint32_t)pvTimerGetTimerID(xTimer);
    
    if (ulTimerID == WATCHDOG_TIMER_ID) {
        /* 喂看门狗 */
        HAL_IWDG_Refresh(&hiwdg);
        
        printf("Watchdog fed at tick: %lu\r\n", xTaskGetTickCount());
    }
}

/**
 * @brief 数据采集定时器回调函数
 * @param xTimer 定时器句柄
 */
void vDataTimerCallback(TimerHandle_t xTimer)
{
    uint32_t ulTimerID = (uint32_t)pvTimerGetTimerID(xTimer);
    
    if (ulTimerID == DATA_TIMER_ID) {
        /* 模拟数据采集 */
        static uint32_t data_counter = 0;
        data_counter++;
        
        printf("Data collection #%lu at tick: %lu\r\n", 
               data_counter, xTaskGetTickCount());
        
        /* 这里可以添加实际的数据采集代码 */
        // ADC_Start();
        // Read_Sensor_Data();
    }
}

/**
 * @brief 创建并启动软件定时器
 */
void vCreateSoftwareTimers(void)
{
    BaseType_t xReturn;
    
    /* 创建LED闪烁定时器 (周期性，500ms) */
    xLedTimer = xTimerCreate(
        "LED Timer",                    // 定时器名称
        pdMS_TO_TICKS(500),            // 定时器周期 (500ms)
        pdTRUE,                        // 自动重载 (周期性)
        (void *)LED_TIMER_ID,          // 定时器ID
        vLedTimerCallback              // 回调函数
    );
    
    if (xLedTimer != NULL) {
        /* 启动定时器 */
        xReturn = xTimerStart(xLedTimer, 0);
        if (xReturn == pdPASS) {
            printf("LED Timer created and started successfully\r\n");
        }
    }
    
    /* 创建看门狗定时器 (周期性，1000ms) */
    xWatchdogTimer = xTimerCreate(
        "Watchdog Timer",
        pdMS_TO_TICKS(1000),           // 1秒周期
        pdTRUE,                        // 自动重载
        (void *)WATCHDOG_TIMER_ID,
        vWatchdogTimerCallback
    );
    
    if (xWatchdogTimer != NULL) {
        xReturn = xTimerStart(xWatchdogTimer, 0);
        if (xReturn == pdPASS) {
            printf("Watchdog Timer created and started successfully\r\n");
        }
    }
    
    /* 创建数据采集定时器 (周期性，2000ms) */
    xDataTimer = xTimerCreate(
        "Data Timer",
        pdMS_TO_TICKS(2000),           // 2秒周期
        pdTRUE,                        // 自动重载
        (void *)DATA_TIMER_ID,
        vDataTimerCallback
    );
    
    if (xDataTimer != NULL) {
        xReturn = xTimerStart(xDataTimer, 0);
        if (xReturn == pdPASS) {
            printf("Data Timer created and started successfully\r\n");
        }
    }
}

/**
 * @brief 定时器控制演示函数
 */
void vTimerControlDemo(void)
{
    /* 停止LED定时器 */
    if (xTimerIsTimerActive(xLedTimer)) {
        xTimerStop(xLedTimer, portMAX_DELAY);
        printf("LED Timer stopped\r\n");
    }
    
    /* 等待2秒 */
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    /* 重新启动LED定时器 */
    xTimerStart(xLedTimer, portMAX_DELAY);
    printf("LED Timer restarted\r\n");
    
    /* 修改数据采集定时器周期为5秒 */
    xTimerChangePeriod(xDataTimer, pdMS_TO_TICKS(5000), portMAX_DELAY);
    printf("Data Timer period changed to 5 seconds\r\n");
    
    /* 重置看门狗定时器 */
    xTimerReset(xWatchdogTimer, portMAX_DELAY);
    printf("Watchdog Timer reset\r\n");
}

/**
 * @brief 一次性定时器演示
 */
void vOneShotTimerDemo(void)
{
    TimerHandle_t xOneShotTimer;
    
    /* 创建一次性定时器 (3秒后执行一次) */
    xOneShotTimer = xTimerCreate(
        "OneShot Timer",
        pdMS_TO_TICKS(3000),           // 3秒延时
        pdFALSE,                       // 一次性 (不自动重载)
        NULL,                          // 无需ID
        vOneShotCallback               // 回调函数
    );
    
    if (xOneShotTimer != NULL) {
        xTimerStart(xOneShotTimer, 0);
        printf("One-shot timer started, will fire in 3 seconds\r\n");
    }
}

/**
 * @brief 一次性定时器回调函数
 */
void vOneShotCallback(TimerHandle_t xTimer)
{
    printf("One-shot timer fired! Performing one-time action...\r\n");
    
    /* 执行一次性操作 */
    // 例如：系统初始化完成后的操作
    // 例如：延时关闭某个功能
    
    /* 一次性定时器执行完后会自动删除，无需手动删除 */
}

/**
 * @brief 在main函数或任务中调用此函数来初始化定时器
 */
void vInitializeSoftwareTimers(void)
{
    printf("Initializing Software Timers...\r\n");
    
    /* 创建并启动定时器 */
    vCreateSoftwareTimers();
    
    /* 演示一次性定时器 */
    vOneShotTimerDemo();
    
    printf("All timers initialized\r\n");
}
