#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
FREERTOS.IPParameters=Tasks01,configUSE_TIMERS,configTIMER_TASK_PRIORITY
FREERTOS.Tasks01=defaultTask,0,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configTIMER_TASK_PRIORITY=4
FREERTOS.configUSE_TIMERS=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103RCT6
Mcu.Family=STM32F1
Mcu.IP0=FREERTOS
Mcu.IP1=IWDG
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IPNb=5
Mcu.Name=STM32F103R(C-D-E)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PC6
Mcu.Pin11=PC7
Mcu.Pin12=PC8
Mcu.Pin13=PC9
Mcu.Pin14=PB8
Mcu.Pin15=VP_FREERTOS_VS_CMSIS_V1
Mcu.Pin16=VP_IWDG_VS_IWDG
Mcu.Pin17=VP_SYS_VS_ND
Mcu.Pin18=VP_SYS_VS_tim1
Mcu.Pin2=PD0-OSC_IN
Mcu.Pin3=PD1-OSC_OUT
Mcu.Pin4=PC0
Mcu.Pin5=PC1
Mcu.Pin6=PC2
Mcu.Pin7=PC3
Mcu.Pin8=PC4
Mcu.Pin9=PC5
Mcu.PinsNb=19
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103RCTx
MxCube.Version=6.9.2
MxDb.Version=DB.6.0.92
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM1_UP_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM1_UP_IRQn
NVIC.TimeBaseIP=TIM1
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PB8.GPIOParameters=GPIO_Label
PB8.GPIO_Label=Beep_pin
PB8.Locked=true
PB8.Signal=GPIO_Output
PC0.GPIOParameters=PinState,GPIO_Label
PC0.GPIO_Label=LED1_pin
PC0.Locked=true
PC0.PinState=GPIO_PIN_SET
PC0.Signal=GPIO_Output
PC1.GPIOParameters=PinState,GPIO_Label
PC1.GPIO_Label=LED2_pin
PC1.Locked=true
PC1.PinState=GPIO_PIN_SET
PC1.Signal=GPIO_Output
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC2.GPIOParameters=PinState,GPIO_Label
PC2.GPIO_Label=LED3_pin
PC2.Locked=true
PC2.PinState=GPIO_PIN_SET
PC2.Signal=GPIO_Output
PC3.GPIOParameters=PinState,GPIO_Label
PC3.GPIO_Label=LED4_pin
PC3.Locked=true
PC3.PinState=GPIO_PIN_SET
PC3.Signal=GPIO_Output
PC4.GPIOParameters=PinState,GPIO_Label
PC4.GPIO_Label=LED5_pin
PC4.Locked=true
PC4.PinState=GPIO_PIN_SET
PC4.Signal=GPIO_Output
PC5.GPIOParameters=PinState,GPIO_Label
PC5.GPIO_Label=LED6_pin
PC5.Locked=true
PC5.PinState=GPIO_PIN_SET
PC5.Signal=GPIO_Output
PC6.GPIOParameters=PinState,GPIO_Label
PC6.GPIO_Label=LED7_pin
PC6.Locked=true
PC6.PinState=GPIO_PIN_SET
PC6.Signal=GPIO_Output
PC7.GPIOParameters=PinState,GPIO_Label
PC7.GPIO_Label=LED8_pin
PC7.Locked=true
PC7.PinState=GPIO_PIN_SET
PC7.Signal=GPIO_Output
PC8.GPIOParameters=GPIO_PuPd,GPIO_Label
PC8.GPIO_Label=KEY_0
PC8.GPIO_PuPd=GPIO_PULLUP
PC8.Locked=true
PC8.Signal=GPIO_Input
PC9.GPIOParameters=GPIO_PuPd,GPIO_Label
PC9.GPIO_Label=KEY_1
PC9.GPIO_PuPd=GPIO_PULLUP
PC9.Locked=true
PC9.Signal=GPIO_Input
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103RCTx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=hal_rtos.ioc
ProjectManager.ProjectName=hal_rtos
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_IWDG_Init-IWDG-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.I2S2Freq_Value=72000000
RCC.I2S3Freq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,I2S2Freq_Value,I2S3Freq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SDIOFreq_Value,SDIOHCLKDiv2FreqValue,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SDIOFreq_Value=72000000
RCC.SDIOHCLKDiv2FreqValue=36000000
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
VP_FREERTOS_VS_CMSIS_V1.Mode=CMSIS_V1
VP_FREERTOS_VS_CMSIS_V1.Signal=FREERTOS_VS_CMSIS_V1
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_SYS_VS_ND.Mode=No_Debug
VP_SYS_VS_ND.Signal=SYS_VS_ND
VP_SYS_VS_tim1.Mode=TIM1
VP_SYS_VS_tim1.Signal=SYS_VS_tim1
board=custom
rtos.0.ip=FREERTOS
