/**
 * @file timer_integration_example.c
 * @brief STM32 + FreeRTOS软件定时器集成示例
 * <AUTHOR>
 * @date 2025-08-03
 */

#include "main.h"
#include "cmsis_os.h"
#include "FreeRTOS.h"
#include "timers.h"
#include "task.h"

/* 定时器句柄声明 */
TimerHandle_t xHeartbeatTimer;
TimerHandle_t xSensorTimer;
TimerHandle_t xDisplayTimer;

/* 任务句柄声明 */
osThreadId_t sensorTaskHandle;
osThreadId_t displayTaskHandle;

/* 全局变量 */
volatile uint32_t heartbeat_counter = 0;
volatile uint32_t sensor_data = 0;
volatile bool display_update_flag = false;

/*==============================================================================
 * 定时器回调函数实现
 *============================================================================*/

/**
 * @brief 心跳定时器回调函数
 * 功能：每500ms切换一次LED状态，表示系统正常运行
 */
void vHeartbeatTimerCallback(TimerHandle_t xTimer)
{
    /* 切换心跳LED */
    HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    
    /* 更新心跳计数器 */
    heartbeat_counter++;
    
    /* 每10次心跳打印一次信息 (5秒) */
    if (heartbeat_counter % 10 == 0) {
        printf("System heartbeat: %lu (uptime: %lu seconds)\r\n", 
               heartbeat_counter, heartbeat_counter / 2);
    }
}

/**
 * @brief 传感器定时器回调函数
 * 功能：每1秒触发传感器数据采集
 */
void vSensorTimerCallback(TimerHandle_t xTimer)
{
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    
    /* 通知传感器任务进行数据采集 */
    if (sensorTaskHandle != NULL) {
        vTaskNotifyGiveFromISR(sensorTaskHandle, &xHigherPriorityTaskWoken);
        
        /* 如果有更高优先级任务被唤醒，进行任务切换 */
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}

/**
 * @brief 显示更新定时器回调函数
 * 功能：每2秒触发显示更新
 */
void vDisplayTimerCallback(TimerHandle_t xTimer)
{
    /* 设置显示更新标志 */
    display_update_flag = true;
    
    /* 通知显示任务更新显示内容 */
    if (displayTaskHandle != NULL) {
        BaseType_t xHigherPriorityTaskWoken = pdFALSE;
        vTaskNotifyGiveFromISR(displayTaskHandle, &xHigherPriorityTaskWoken);
        portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    }
}

/*==============================================================================
 * 任务实现
 *============================================================================*/

/**
 * @brief 传感器数据采集任务
 */
void SensorTask(void *argument)
{
    uint32_t notification_value;
    
    printf("Sensor Task started\r\n");
    
    for (;;) {
        /* 等待定时器通知 */
        notification_value = ulTaskNotifyTake(pdTRUE, portMAX_DELAY);
        
        if (notification_value > 0) {
            /* 模拟传感器数据采集 */
            sensor_data = HAL_GetTick() % 1000;  // 模拟数据
            
            printf("Sensor data collected: %lu\r\n", sensor_data);
            
            /* 这里可以添加实际的传感器读取代码 */
            // sensor_data = Read_Temperature_Sensor();
            // sensor_data = Read_ADC_Value();
        }
    }
}

/**
 * @brief 显示更新任务
 */
void DisplayTask(void *argument)
{
    uint32_t notification_value;
    
    printf("Display Task started\r\n");
    
    for (;;) {
        /* 等待定时器通知 */
        notification_value = ulTaskNotifyTake(pdTRUE, portMAX_DELAY);
        
        if (notification_value > 0 && display_update_flag) {
            /* 更新显示内容 */
            printf("=== Display Update ===\r\n");
            printf("Heartbeat: %lu\r\n", heartbeat_counter);
            printf("Sensor Data: %lu\r\n", sensor_data);
            printf("Free Heap: %lu bytes\r\n", xPortGetFreeHeapSize());
            printf("System Tick: %lu\r\n", xTaskGetTickCount());
            printf("=====================\r\n");
            
            /* 清除更新标志 */
            display_update_flag = false;
            
            /* 这里可以添加实际的显示更新代码 */
            // Update_LCD_Display();
            // Update_OLED_Display();
        }
    }
}

/*==============================================================================
 * 定时器初始化和管理函数
 *============================================================================*/

/**
 * @brief 创建所有软件定时器
 */
void vCreateAllTimers(void)
{
    BaseType_t xReturn;
    
    /* 创建心跳定时器 (500ms周期) */
    xHeartbeatTimer = xTimerCreate(
        "Heartbeat",                    // 定时器名称
        pdMS_TO_TICKS(500),            // 500ms周期
        pdTRUE,                        // 自动重载
        (void *)1,                     // 定时器ID
        vHeartbeatTimerCallback        // 回调函数
    );
    
    if (xHeartbeatTimer != NULL) {
        xReturn = xTimerStart(xHeartbeatTimer, 0);
        if (xReturn == pdPASS) {
            printf("Heartbeat timer created and started\r\n");
        } else {
            printf("Failed to start heartbeat timer\r\n");
        }
    } else {
        printf("Failed to create heartbeat timer\r\n");
    }
    
    /* 创建传感器定时器 (1000ms周期) */
    xSensorTimer = xTimerCreate(
        "Sensor",
        pdMS_TO_TICKS(1000),           // 1秒周期
        pdTRUE,                        // 自动重载
        (void *)2,                     // 定时器ID
        vSensorTimerCallback
    );
    
    if (xSensorTimer != NULL) {
        xReturn = xTimerStart(xSensorTimer, 0);
        if (xReturn == pdPASS) {
            printf("Sensor timer created and started\r\n");
        }
    }
    
    /* 创建显示定时器 (2000ms周期) */
    xDisplayTimer = xTimerCreate(
        "Display",
        pdMS_TO_TICKS(2000),           // 2秒周期
        pdTRUE,                        // 自动重载
        (void *)3,                     // 定时器ID
        vDisplayTimerCallback
    );
    
    if (xDisplayTimer != NULL) {
        xReturn = xTimerStart(xDisplayTimer, 0);
        if (xReturn == pdPASS) {
            printf("Display timer created and started\r\n");
        }
    }
}

/**
 * @brief 创建应用任务
 */
void vCreateApplicationTasks(void)
{
    /* 创建传感器任务 */
    const osThreadAttr_t sensorTask_attributes = {
        .name = "SensorTask",
        .stack_size = 128 * 4,
        .priority = (osPriority_t) osPriorityNormal,
    };
    sensorTaskHandle = osThreadNew(SensorTask, NULL, &sensorTask_attributes);
    
    /* 创建显示任务 */
    const osThreadAttr_t displayTask_attributes = {
        .name = "DisplayTask",
        .stack_size = 128 * 4,
        .priority = (osPriority_t) osPriorityNormal,
    };
    displayTaskHandle = osThreadNew(DisplayTask, NULL, &displayTask_attributes);
}

/**
 * @brief 定时器控制函数示例
 */
void vTimerControlExample(void)
{
    /* 暂停心跳定时器 */
    if (xTimerIsTimerActive(xHeartbeatTimer)) {
        xTimerStop(xHeartbeatTimer, portMAX_DELAY);
        printf("Heartbeat timer paused\r\n");
    }
    
    /* 等待3秒 */
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    /* 恢复心跳定时器 */
    xTimerStart(xHeartbeatTimer, portMAX_DELAY);
    printf("Heartbeat timer resumed\r\n");
    
    /* 修改传感器定时器周期为500ms */
    xTimerChangePeriod(xSensorTimer, pdMS_TO_TICKS(500), portMAX_DELAY);
    printf("Sensor timer period changed to 500ms\r\n");
}

/**
 * @brief 系统初始化函数
 * 在main函数中调用此函数来初始化整个定时器系统
 */
void vInitializeTimerSystem(void)
{
    printf("Initializing Timer System...\r\n");
    
    /* 创建应用任务 */
    vCreateApplicationTasks();
    
    /* 创建并启动定时器 */
    vCreateAllTimers();
    
    printf("Timer System initialized successfully\r\n");
    printf("System ready - timers running\r\n");
}

/**
 * @brief 获取系统状态信息
 */
void vPrintSystemStatus(void)
{
    printf("\r\n=== System Status ===\r\n");
    printf("Heartbeat Counter: %lu\r\n", heartbeat_counter);
    printf("Latest Sensor Data: %lu\r\n", sensor_data);
    printf("Free Heap Size: %lu bytes\r\n", xPortGetFreeHeapSize());
    printf("System Uptime: %lu ticks\r\n", xTaskGetTickCount());
    
    /* 检查定时器状态 */
    printf("Timer Status:\r\n");
    printf("  Heartbeat: %s\r\n", xTimerIsTimerActive(xHeartbeatTimer) ? "Active" : "Inactive");
    printf("  Sensor: %s\r\n", xTimerIsTimerActive(xSensorTimer) ? "Active" : "Inactive");
    printf("  Display: %s\r\n", xTimerIsTimerActive(xDisplayTimer) ? "Active" : "Inactive");
    printf("====================\r\n\r\n");
}
