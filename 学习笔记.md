# RTOS学习笔记

## 1. 任务通知 (Task Notification)

### 概念
轻量级任务间通信，每个任务有32位通知值，比队列快45%。

### 核心API
- `vTaskNotifyGiveFromISR(handle, &flag)` - 中断发送通知
- `ulTaskNotifyTake(pdTRUE, timeout)` - 任务等待通知

### 典型用法
中断 → 发送通知 → 唤醒任务处理事件。适合一对一通信场景。

## 2. 软件定时器 (Software Timer)

### 概念
软件定时器是RTOS提供的一种定时机制，允许在指定时间后执行回调函数。
- **不占用硬件定时器资源** - 基于系统时钟节拍实现
- **轻量级** - 相比创建专门的任务更节省内存
- **精度依赖系统节拍** - 精度受configTICK_RATE_HZ影响

### 定时器类型
1. **一次性定时器 (One-shot)** - 超时后自动停止
2. **周期性定时器 (Auto-reload)** - 超时后自动重新启动

### 核心API
- `xTimerCreate(name, period, auto_reload, id, callback)` - 创建定时器
- `xTimerStart(timer, block_time)` - 启动定时器
- `xTimerStop(timer, block_time)` - 停止定时器
- `xTimerReset(timer, block_time)` - 重置定时器
- `xTimerChangePeriod(timer, new_period, block_time)` - 修改周期

### 典型应用场景
- LED闪烁控制
- 看门狗喂狗
- 定期数据采集
- 超时检测
- 状态机状态切换

### CubeMX配置要求
在STM32CubeMX中配置路径：**Middleware → FREERTOS → Config parameters**
- **USE_TIMERS**: Enabled (启用软件定时器功能)
- **TIMER_TASK_PRIORITY**: 3-4 (定时器任务优先级，建议设置较高)
- **TIMER_TASK_STACK_DEPTH**: 256 (定时器任务栈大小，以字为单位)
- **TIMER_QUEUE_LENGTH**: 10 (定时器命令队列长度)

### 最佳实践
1. **回调函数要简短** - 避免阻塞操作，复杂处理用任务通知
2. **合理设置优先级** - 定时器任务优先级要高于普通任务
3. **注意精度限制** - 最小间隔受系统节拍频率限制
4. **及时清理资源** - 不用的定时器要及时删除

## 3. 互斥量 (Mutex)

### 概念
互斥量是一种同步机制，用于保护共享资源，确保同一时间只有一个任务能访问临界区。
- **互斥访问** - 同时只允许一个任务持有
- **优先级继承** - 防止优先级反转问题
- **递归支持** - 同一任务可多次获取(递归互斥量)

### 与二值信号量的区别
- **所有权** - 互斥量有所有者概念，只有获取者能释放
- **优先级继承** - 互斥量支持优先级继承机制
- **递归** - 互斥量支持递归获取
- **用途** - 互斥量专门用于保护资源，信号量用于同步

### 核心API
- `xSemaphoreCreateMutex()` - 创建互斥量
- `xSemaphoreCreateRecursiveMutex()` - 创建递归互斥量
- `xSemaphoreTake(mutex, timeout)` - 获取互斥量
- `xSemaphoreGive(mutex)` - 释放互斥量
- `xSemaphoreTakeRecursive()` - 递归获取
- `xSemaphoreGiveRecursive()` - 递归释放

### 优先级继承机制
当低优先级任务持有互斥量时，如果高优先级任务等待该互斥量：
1. 低优先级任务临时提升到高优先级任务的优先级
2. 避免中等优先级任务抢占，防止优先级反转
3. 释放互斥量后恢复原优先级

### CubeMX配置要求
在STM32CubeMX中配置路径：**Middleware → FREERTOS → Config parameters**
- **USE_MUTEXES**: Enabled (启用互斥量功能)
- **USE_RECURSIVE_MUTEXES**: Enabled (启用递归互斥量，可选)

### 典型应用场景
- 保护共享变量(如全局计数器)
- 保护硬件资源(如UART、SPI)
- 保护数据结构(如链表、缓冲区)
- 保护文件系统访问
- 保护显示设备访问

### 使用模式
```c
// 获取互斥量 → 访问共享资源 → 释放互斥量
if (xSemaphoreTake(xMutex, portMAX_DELAY) == pdTRUE) {
    // 临界区代码
    shared_resource++;
    xSemaphoreGive(xMutex);
}
```

## 4. 任务基本操作

### 任务状态详解

#### 1. 运行态 (Running)
- **定义**：任务正在CPU上执行
- **特点**：单核系统中同时只有一个任务处于运行态
- **转换**：被更高优先级任务抢占 → 就绪态；主动延时/等待 → 阻塞态

#### 2. 就绪态 (Ready)
- **定义**：任务准备好运行，等待CPU调度
- **特点**：按优先级排队，同优先级按时间片轮转
- **转换**：获得CPU → 运行态；被暂停 → 暂停态

#### 3. 阻塞态 (Blocked)
- **定义**：任务等待某个事件或延时到期
- **阻塞原因**：
  - `vTaskDelay()` - 延时阻塞
  - `xQueueReceive()` - 等待队列数据
  - `xSemaphoreTake()` - 等待信号量/互斥量
  - `ulTaskNotifyTake()` - 等待任务通知
- **转换**：事件发生/延时到期 → 就绪态

#### 4. 暂停态 (Suspended)
- **定义**：任务被手动暂停，完全不参与调度
- **特点**：即使事件发生也不会被唤醒
- **转换**：只能通过`vTaskResume()`恢复到就绪态

#### 5. 删除态 (Deleted)
- **定义**：任务被删除，等待系统回收资源
- **特点**：任务已不存在，内存等待空闲任务回收
- **注意**：删除后任务句柄失效

### 状态转换图
```
创建 → 就绪态 ⟷ 运行态
         ↕      ↕
      暂停态   阻塞态
         ↓      ↓
        删除态 ←←←
```

### 状态查询API
- `eTaskGetState(handle)` - 获取任务状态
- 返回值：`eRunning`, `eReady`, `eBlocked`, `eSuspended`, `eDeleted`

### 任务创建
```c
// 动态创建
TaskHandle_t xTaskHandle;
xTaskCreate(TaskFunction, "TaskName", StackSize, Parameters, Priority, &xTaskHandle);

// 静态创建 (需要提供栈和TCB内存)
StaticTask_t xTaskBuffer;
StackType_t xStack[STACK_SIZE];
xTaskCreateStatic(TaskFunction, "TaskName", STACK_SIZE, Parameters, Priority, xStack, &xTaskBuffer);
```

### 核心操作API
- `xTaskCreate()` - 动态创建任务
- `vTaskDelete(handle)` - 删除任务 (NULL删除自己)
- `vTaskSuspend(handle)` - 暂停任务
- `vTaskResume(handle)` - 恢复任务
- `vTaskSuspendAll()` - 暂停所有任务调度
- `xTaskResumeAll()` - 恢复任务调度

### 任务控制
- `vTaskDelay(ticks)` - 延时指定节拍数
- `vTaskDelayUntil(&lastTime, period)` - 周期性延时
- `taskYIELD()` - 主动让出CPU
- `vTaskPrioritySet(handle, priority)` - 设置优先级
- `uxTaskPriorityGet(handle)` - 获取优先级

### 任务信息查询
- `xTaskGetCurrentTaskHandle()` - 获取当前任务句柄
- `pcTaskGetName(handle)` - 获取任务名称
- `eTaskGetState(handle)` - 获取任务状态
- `uxTaskGetStackHighWaterMark(handle)` - 获取栈使用情况

### 典型使用场景
```c
void TaskFunction(void *pvParameters) {
    for (;;) {
        // 任务主循环
        DoSomeWork();
        vTaskDelay(pdMS_TO_TICKS(100)); // 延时100ms
    }
}

// 创建任务
xTaskCreate(TaskFunction, "MyTask", 128, NULL, 2, &taskHandle);

// 运行一段时间后暂停
vTaskSuspend(taskHandle);

// 需要时恢复
vTaskResume(taskHandle);

// 不需要时删除
vTaskDelete(taskHandle);
```
