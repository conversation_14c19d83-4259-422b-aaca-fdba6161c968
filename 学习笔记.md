# RTOS学习笔记

## 1. 任务通知 (Task Notification)

### 概念
轻量级任务间通信，每个任务有32位通知值，比队列快45%。

### 核心API
- `vTaskNotifyGiveFromISR(handle, &flag)` - 中断发送通知
- `ulTaskNotifyTake(pdTRUE, timeout)` - 任务等待通知

### 典型用法
中断 → 发送通知 → 唤醒任务处理事件。适合一对一通信场景。

## 2. 软件定时器 (Software Timer)

### 概念
软件定时器是RTOS提供的一种定时机制，允许在指定时间后执行回调函数。
- **不占用硬件定时器资源** - 基于系统时钟节拍实现
- **轻量级** - 相比创建专门的任务更节省内存
- **精度依赖系统节拍** - 精度受configTICK_RATE_HZ影响

### 定时器类型
1. **一次性定时器 (One-shot)** - 超时后自动停止
2. **周期性定时器 (Auto-reload)** - 超时后自动重新启动

### 核心API
- `xTimerCreate(name, period, auto_reload, id, callback)` - 创建定时器
- `xTimerStart(timer, block_time)` - 启动定时器
- `xTimerStop(timer, block_time)` - 停止定时器
- `xTimerReset(timer, block_time)` - 重置定时器
- `xTimerChangePeriod(timer, new_period, block_time)` - 修改周期

### 定时器服务任务
- FreeRTOS内部创建专门的定时器服务任务来管理所有软件定时器
- 优先级由`configTIMER_TASK_PRIORITY`配置
- 栈大小由`configTIMER_TASK_STACK_DEPTH`配置

### 典型应用场景
- LED闪烁控制
- 看门狗喂狗
- 定期数据采集
- 超时检测
- 状态机状态切换

### 配置要求 (FreeRTOSConfig.h)
```c
#define configUSE_TIMERS                1
#define configTIMER_TASK_PRIORITY       (configMAX_PRIORITIES - 1)
#define configTIMER_TASK_STACK_DEPTH    (configMINIMAL_STACK_SIZE * 2)
#define configTIMER_QUEUE_LENGTH        10
```

### 最佳实践
1. **回调函数要简短** - 避免阻塞操作，复杂处理用任务通知
2. **合理设置优先级** - 定时器任务优先级要高于普通任务
3. **注意精度限制** - 最小间隔受系统节拍频率限制
4. **及时清理资源** - 不用的定时器要及时删除

### 常见错误
- 在回调函数中调用阻塞API
- 定时器任务优先级设置过低
- 忘记启用configUSE_TIMERS
- 回调函数执行时间过长

### 调试技巧
- 使用`xTimerIsTimerActive()`检查定时器状态
- 监控定时器服务任务的栈使用情况
- 在回调函数中添加计数器统计执行次数