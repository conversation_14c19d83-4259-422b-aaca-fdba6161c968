# RTOS学习笔记2 - 事件组

## 1. 事件组 (Event Group)

### 概念
事件组是一种同步机制，允许任务等待多个事件的组合。每个事件组包含24个事件位，每个位代表一个事件的发生状态。
- **多事件同步** - 可以等待多个事件同时发生或任一发生
- **位操作** - 基于位掩码操作，高效简洁
- **广播特性** - 一个事件可以同时唤醒多个等待的任务

### 核心特点
- **24个事件位** - 每个事件组有24个可用位（bit 0-23）
- **原子操作** - 位的设置和清除是原子的
- **灵活等待** - 支持AND/OR逻辑等待
- **自动清除** - 可选择等待成功后自动清除事件位

### 与其他同步机制的区别
| 机制 | 用途 | 特点 |
|------|------|------|
| 信号量 | 计数/二值同步 | 单一事件，有计数值 |
| 队列 | 数据传递 | 传递数据，FIFO |
| 任务通知 | 轻量级通知 | 32位值，一对一 |
| 事件组 | 多事件同步 | 24位事件，多对多 |

### 核心API
- `xEventGroupCreate()` - 创建事件组
- `xEventGroupSetBits(group, bits)` - 设置事件位
- `xEventGroupSetBitsFromISR(group, bits, &flag)` - 中断中设置事件位
- `xEventGroupWaitBits(group, bits, clear, waitAll, timeout)` - 等待事件位
- `xEventGroupClearBits(group, bits)` - 清除事件位
- `xEventGroupGetBits(group)` - 获取当前事件位状态

### 等待模式
```c
// AND等待 - 所有指定事件都发生才返回
xEventGroupWaitBits(xEventGroup, BIT_0 | BIT_1 | BIT_2, pdTRUE, pdTRUE, timeout);

// OR等待 - 任一指定事件发生就返回  
xEventGroupWaitBits(xEventGroup, BIT_0 | BIT_1 | BIT_2, pdTRUE, pdFALSE, timeout);
```

### CubeMX配置要求
在STM32CubeMX中配置路径：**Middleware → FREERTOS → Config parameters**
- **USE_EVENT_GROUPS**: Enabled (启用事件组功能)

### 典型应用场景

#### 1. 系统初始化同步
```c
#define INIT_WIFI_BIT    (1 << 0)
#define INIT_SD_BIT      (1 << 1) 
#define INIT_SENSOR_BIT  (1 << 2)

// 等待所有初始化完成
xEventGroupWaitBits(xInitEventGroup, 
                   INIT_WIFI_BIT | INIT_SD_BIT | INIT_SENSOR_BIT,
                   pdFALSE, pdTRUE, portMAX_DELAY);
```

#### 2. 多传感器数据就绪
```c
#define TEMP_READY_BIT   (1 << 0)
#define HUMID_READY_BIT  (1 << 1)
#define PRESS_READY_BIT  (1 << 2)

// 等待任一传感器数据就绪
EventBits_t bits = xEventGroupWaitBits(xSensorEventGroup,
                                      TEMP_READY_BIT | HUMID_READY_BIT | PRESS_READY_BIT,
                                      pdTRUE, pdFALSE, pdMS_TO_TICKS(1000));
```

#### 3. 状态机事件驱动
```c
#define START_EVENT_BIT  (1 << 0)
#define STOP_EVENT_BIT   (1 << 1)
#define ERROR_EVENT_BIT  (1 << 2)
#define RESET_EVENT_BIT  (1 << 3)

// 状态机等待任一控制事件
EventBits_t events = xEventGroupWaitBits(xControlEventGroup,
                                        START_EVENT_BIT | STOP_EVENT_BIT | ERROR_EVENT_BIT | RESET_EVENT_BIT,
                                        pdTRUE, pdFALSE, portMAX_DELAY);
```

### 使用注意事项
1. **事件位定义** - 使用宏定义清晰标识每个事件位
2. **避免竞争** - 多个任务设置同一事件位时注意同步
3. **及时清除** - 根据需要选择是否自动清除事件位
4. **中断安全** - 中断中只能使用FromISR版本的API

### 最佳实践
1. **位定义规范** - 使用有意义的宏名称
2. **逻辑清晰** - 明确AND/OR等待逻辑
3. **超时处理** - 合理设置等待超时时间
4. **状态检查** - 检查返回的事件位确定具体事件

### 实际应用示例
```c
// 定义事件位
#define WIFI_CONNECTED_BIT    (1 << 0)
#define DATA_READY_BIT        (1 << 1)
#define UPLOAD_COMPLETE_BIT   (1 << 2)

EventGroupHandle_t xSystemEventGroup;

void SystemTask(void *pvParameters) {
    // 创建事件组
    xSystemEventGroup = xEventGroupCreate();
    
    for (;;) {
        // 等待WiFi连接且数据就绪
        EventBits_t bits = xEventGroupWaitBits(
            xSystemEventGroup,
            WIFI_CONNECTED_BIT | DATA_READY_BIT,  // 等待的事件
            pdTRUE,   // 等待成功后清除事件位
            pdTRUE,   // AND等待（两个事件都要发生）
            pdMS_TO_TICKS(5000)  // 5秒超时
        );
        
        if ((bits & (WIFI_CONNECTED_BIT | DATA_READY_BIT)) == (WIFI_CONNECTED_BIT | DATA_READY_BIT)) {
            // 两个条件都满足，开始上传数据
            UploadData();
            // 设置上传完成事件
            xEventGroupSetBits(xSystemEventGroup, UPLOAD_COMPLETE_BIT);
        }
    }
}

// WiFi任务设置连接事件
void WiFiTask(void *pvParameters) {
    if (WiFi_Connect() == SUCCESS) {
        xEventGroupSetBits(xSystemEventGroup, WIFI_CONNECTED_BIT);
    }
}

// 传感器任务设置数据就绪事件
void SensorTask(void *pvParameters) {
    if (ReadSensorData() == SUCCESS) {
        xEventGroupSetBits(xSystemEventGroup, DATA_READY_BIT);
    }
}
```
