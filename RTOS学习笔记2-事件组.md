# RTOS学习笔记2 - 事件组

## 1. 事件组 (Event Group)

### 概念
事件组是一种同步机制，允许任务等待多个事件的组合。每个事件组包含24个事件位，每个位代表一个事件的发生状态。
- **多事件同步** - 可以等待多个事件同时发生或任一发生
- **位操作** - 基于位掩码操作，高效简洁
- **广播特性** - 一个事件可以同时唤醒多个等待的任务

### 核心特点
- **24个事件位** - 每个事件组有24个可用位（bit 0-23）
- **原子操作** - 位的设置和清除是原子的
- **灵活等待** - 支持AND/OR逻辑等待
- **自动清除** - 可选择等待成功后自动清除事件位

### 与其他同步机制的区别
| 机制 | 用途 | 特点 |
|------|------|------|
| 信号量 | 计数/二值同步 | 单一事件，有计数值 |
| 队列 | 数据传递 | 传递数据，FIFO |
| 任务通知 | 轻量级通知 | 32位值，一对一 |
| 事件组 | 多事件同步 | 24位事件，多对多 |

### 核心API
- `xEventGroupCreate()` - 创建事件组
- `xEventGroupSetBits(group, bits)` - 设置事件位
- `xEventGroupSetBitsFromISR(group, bits, &flag)` - 中断中设置事件位
- `xEventGroupWaitBits(group, bits, clear, waitAll, timeout)` - 等待事件位
- `xEventGroupClearBits(group, bits)` - 清除事件位
- `xEventGroupGetBits(group)` - 获取当前事件位状态

### 等待模式
```c
// AND等待 - 所有指定事件都发生才返回
xEventGroupWaitBits(xEventGroup, BIT_0 | BIT_1 | BIT_2, pdTRUE, pdTRUE, timeout);

// OR等待 - 任一指定事件发生就返回  
xEventGroupWaitBits(xEventGroup, BIT_0 | BIT_1 | BIT_2, pdTRUE, pdFALSE, timeout);
```

### CubeMX配置要求
在STM32CubeMX中配置路径：**Middleware → FREERTOS → Config parameters**
- **USE_EVENT_GROUPS**: Enabled (启用事件组功能)

### 典型应用场景

#### 1. 系统初始化同步
```c
#define INIT_WIFI_BIT    (1 << 0)
#define INIT_SD_BIT      (1 << 1) 
#define INIT_SENSOR_BIT  (1 << 2)

// 等待所有初始化完成
xEventGroupWaitBits(xInitEventGroup, 
                   INIT_WIFI_BIT | INIT_SD_BIT | INIT_SENSOR_BIT,
                   pdFALSE, pdTRUE, portMAX_DELAY);
```

#### 2. 多传感器数据就绪
```c
#define TEMP_READY_BIT   (1 << 0)
#define HUMID_READY_BIT  (1 << 1)
#define PRESS_READY_BIT  (1 << 2)

// 等待任一传感器数据就绪
EventBits_t bits = xEventGroupWaitBits(xSensorEventGroup,
                                      TEMP_READY_BIT | HUMID_READY_BIT | PRESS_READY_BIT,
                                      pdTRUE, pdFALSE, pdMS_TO_TICKS(1000));
```

#### 3. 状态机事件驱动
```c
#define START_EVENT_BIT  (1 << 0)
#define STOP_EVENT_BIT   (1 << 1)
#define ERROR_EVENT_BIT  (1 << 2)
#define RESET_EVENT_BIT  (1 << 3)

// 状态机等待任一控制事件
EventBits_t events = xEventGroupWaitBits(xControlEventGroup,
                                        START_EVENT_BIT | STOP_EVENT_BIT | ERROR_EVENT_BIT | RESET_EVENT_BIT,
                                        pdTRUE, pdFALSE, portMAX_DELAY);
```

### 使用注意事项
1. **事件位定义** - 使用宏定义清晰标识每个事件位
2. **避免竞争** - 多个任务设置同一事件位时注意同步
3. **及时清除** - 根据需要选择是否自动清除事件位
4. **中断安全** - 中断中只能使用FromISR版本的API

### 最佳实践
1. **位定义规范** - 使用有意义的宏名称
2. **逻辑清晰** - 明确AND/OR等待逻辑
3. **超时处理** - 合理设置等待超时时间
4. **状态检查** - 检查返回的事件位确定具体事件

### 实际应用示例
```c
// 定义事件位
#define WIFI_CONNECTED_BIT    (1 << 0)
#define DATA_READY_BIT        (1 << 1)
#define UPLOAD_COMPLETE_BIT   (1 << 2)

EventGroupHandle_t xSystemEventGroup;

void SystemTask(void *pvParameters) {
    // 创建事件组
    xSystemEventGroup = xEventGroupCreate();
    
    for (;;) {
        // 等待WiFi连接且数据就绪
        EventBits_t bits = xEventGroupWaitBits(
            xSystemEventGroup,
            WIFI_CONNECTED_BIT | DATA_READY_BIT,  // 等待的事件
            pdTRUE,   // 等待成功后清除事件位
            pdTRUE,   // AND等待（两个事件都要发生）
            pdMS_TO_TICKS(5000)  // 5秒超时
        );
        
        if ((bits & (WIFI_CONNECTED_BIT | DATA_READY_BIT)) == (WIFI_CONNECTED_BIT | DATA_READY_BIT)) {
            // 两个条件都满足，开始上传数据
            UploadData();
            // 设置上传完成事件
            xEventGroupSetBits(xSystemEventGroup, UPLOAD_COMPLETE_BIT);
        }
    }
}

// WiFi任务设置连接事件
void WiFiTask(void *pvParameters) {
    if (WiFi_Connect() == SUCCESS) {
        xEventGroupSetBits(xSystemEventGroup, WIFI_CONNECTED_BIT);
    }
}

// 传感器任务设置数据就绪事件
void SensorTask(void *pvParameters) {
    if (ReadSensorData() == SUCCESS) {
        xEventGroupSetBits(xSystemEventGroup, DATA_READY_BIT);
    }
}
```

## 2. 消息队列 (Queue)

### 概念
消息队列是任务间通信的核心机制，用于在任务之间安全地传递数据。它是一个FIFO（先进先出）的数据缓冲区。
- **数据传递** - 不仅同步任务，还传递具体数据
- **FIFO特性** - 先发送的数据先被接收
- **阻塞机制** - 队列满时发送阻塞，队列空时接收阻塞
- **任务安全** - 多任务访问时自动保护

### 核心特点
- **固定大小** - 创建时指定队列长度和每个消息的大小
- **拷贝传递** - 传递数据的副本，不是指针
- **原子操作** - 发送和接收操作是原子的
- **优先级继承** - 支持优先级继承机制

### 与其他机制的区别
| 机制 | 数据传递 | 同步 | 容量 | 用途 |
|------|----------|------|------|------|
| 任务通知 | 32位值 | ✓ | 1个值 | 轻量级一对一 |
| 信号量 | 无 | ✓ | 计数值 | 同步/互斥 |
| 事件组 | 无 | ✓ | 24位状态 | 多事件同步 |
| 消息队列 | 任意数据 | ✓ | 多个消息 | 数据传递 |

### 核心API
- `xQueueCreate(length, itemSize)` - 创建队列
- `xQueueSend(queue, item, timeout)` - 发送到队列尾部
- `xQueueSendToFront(queue, item, timeout)` - 发送到队列头部
- `xQueueReceive(queue, buffer, timeout)` - 接收数据
- `xQueuePeek(queue, buffer, timeout)` - 查看数据但不移除
- `uxQueueMessagesWaiting(queue)` - 查询队列中消息数量

### 中断安全版本
- `xQueueSendFromISR(queue, item, &flag)` - 中断中发送
- `xQueueReceiveFromISR(queue, buffer, &flag)` - 中断中接收

### CubeMX配置要求
在STM32CubeMX中配置路径：**Middleware → FREERTOS → Config parameters**
- **USE_QUEUE_SETS**: Enabled (可选，启用队列集功能)

### 典型应用场景

#### 1. 传感器数据传递
```c
typedef struct {
    uint16_t temperature;
    uint16_t humidity;
    uint32_t timestamp;
} SensorData_t;

QueueHandle_t xSensorQueue;

// 创建队列（可存储10个传感器数据）
xSensorQueue = xQueueCreate(10, sizeof(SensorData_t));

// 发送数据
SensorData_t data = {25, 60, xTaskGetTickCount()};
xQueueSend(xSensorQueue, &data, portMAX_DELAY);

// 接收数据
SensorData_t receivedData;
if (xQueueReceive(xSensorQueue, &receivedData, pdMS_TO_TICKS(1000)) == pdTRUE) {
    ProcessSensorData(&receivedData);
}
```

#### 2. 命令处理系统
```c
typedef enum {
    CMD_START,
    CMD_STOP,
    CMD_RESET,
    CMD_CONFIG
} CommandType_t;

typedef struct {
    CommandType_t type;
    uint32_t parameter;
    TaskHandle_t sender;
} Command_t;

QueueHandle_t xCommandQueue;

// 发送命令
Command_t cmd = {CMD_START, 100, xTaskGetCurrentTaskHandle()};
xQueueSend(xCommandQueue, &cmd, portMAX_DELAY);
```

#### 3. 日志系统
```c
typedef struct {
    char message[64];
    uint8_t level;  // 0=DEBUG, 1=INFO, 2=WARN, 3=ERROR
    uint32_t timestamp;
} LogMessage_t;

QueueHandle_t xLogQueue;

// 发送日志
LogMessage_t log;
snprintf(log.message, sizeof(log.message), "System started");
log.level = 1;  // INFO
log.timestamp = xTaskGetTickCount();
xQueueSend(xLogQueue, &log, 0);  // 非阻塞发送
```

### 队列操作模式

#### 1. 阻塞模式
```c
// 发送端 - 队列满时等待
xQueueSend(xQueue, &data, portMAX_DELAY);  // 永久等待

// 接收端 - 队列空时等待
xQueueReceive(xQueue, &buffer, portMAX_DELAY);  // 永久等待
```

#### 2. 非阻塞模式
```c
// 立即返回，不等待
if (xQueueSend(xQueue, &data, 0) == pdTRUE) {
    // 发送成功
} else {
    // 队列满，发送失败
}
```

#### 3. 超时模式
```c
// 等待指定时间
if (xQueueReceive(xQueue, &buffer, pdMS_TO_TICKS(500)) == pdTRUE) {
    // 500ms内收到数据
} else {
    // 超时，没有数据
}
```

### 使用注意事项
1. **数据拷贝** - 队列传递的是数据副本，不是指针
2. **内存管理** - 队列会自动管理内存，无需手动释放
3. **大数据传递** - 对于大数据，考虑传递指针而非数据本身
4. **队列大小** - 合理设置队列长度，避免内存浪费

### 最佳实践
1. **结构化数据** - 使用结构体封装相关数据
2. **错误处理** - 检查API返回值，处理队列满/空情况
3. **超时设置** - 根据应用需求设置合理的超时时间
4. **队列监控** - 定期检查队列使用情况，避免溢出

### 实际应用示例
```c
// 完整的生产者-消费者示例
typedef struct {
    uint16_t sensorId;
    float value;
    uint32_t timestamp;
} SensorReading_t;

QueueHandle_t xDataQueue;

// 生产者任务（传感器读取）
void SensorTask(void *pvParameters) {
    SensorReading_t reading;

    // 创建队列
    xDataQueue = xQueueCreate(20, sizeof(SensorReading_t));

    for (;;) {
        // 读取传感器数据
        reading.sensorId = 1;
        reading.value = ReadTemperature();
        reading.timestamp = xTaskGetTickCount();

        // 发送到队列
        if (xQueueSend(xDataQueue, &reading, pdMS_TO_TICKS(100)) != pdTRUE) {
            printf("Queue full, data lost!\r\n");
        }

        vTaskDelay(pdMS_TO_TICKS(1000));  // 1秒采集一次
    }
}

// 消费者任务（数据处理）
void ProcessTask(void *pvParameters) {
    SensorReading_t reading;

    for (;;) {
        // 从队列接收数据
        if (xQueueReceive(xDataQueue, &reading, portMAX_DELAY) == pdTRUE) {
            // 处理数据
            printf("Sensor %d: %.2f at %lu\r\n",
                   reading.sensorId, reading.value, reading.timestamp);

            // 数据存储、上传等处理
            StoreToDatabase(&reading);
        }
    }
}

// 中断服务程序中发送紧急数据
void EXTI_IRQHandler(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    SensorReading_t emergency = {99, -999.0, xTaskGetTickCountFromISR()};

    xQueueSendFromISR(xDataQueue, &emergency, &xHigherPriorityTaskWoken);
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}
```
