/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "gpio.h"
#include <string.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
// 数码管段码定义 (共阴极数码管，0表示亮，1表示灭)
#define SEG_0    0xC0  // 显示0
#define SEG_1    0xF9  // 显示1
#define SEG_2    0xA4  // 显示2
#define SEG_3    0xB0  // 显示3
#define SEG_4    0x99  // 显示4
#define SEG_5    0x92  // 显示5
#define SEG_6    0x82  // 显示6
#define SEG_7    0xF8  // 显示7
#define SEG_8    0x80  // 显示8
#define SEG_9    0x90  // 显示9
#define SEG_A    0x88  // 显示A
#define SEG_B    0x83  // 显示b
#define SEG_C    0xC6  // 显示C
#define SEG_D    0xA1  // 显示d
#define SEG_E    0x86  // 显示E
#define SEG_F    0x8E  // 显示F
#define SEG_OFF  0xFF  // 熄灭
#define SEG_DASH 0xBF  // 显示-

// 数码管位数定义
#define DIGIT_COUNT 8
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
// 数码管显示相关变量
static uint8_t display_buffer[DIGIT_COUNT];  // 显示缓冲区
static uint8_t current_digit = 0;            // 当前扫描的数码管位
static uint8_t digit_segments[] = {SEG_0, SEG_1, SEG_2, SEG_3, SEG_4, SEG_5, SEG_6, SEG_7, SEG_8, SEG_9,
                                   SEG_A, SEG_B, SEG_C, SEG_D, SEG_E, SEG_F, SEG_OFF, SEG_DASH};

// 数字到段码的映射
static uint8_t get_segment_code(uint8_t value) {
    if (value <= 15) return digit_segments[value];
    if (value == 0xFF) return SEG_OFF;
    if (value == 0xFE) return SEG_DASH;
    return SEG_OFF;
}
/* USER CODE END Variables */
osThreadId MonitorTaskHandle;
osThreadId DisplayTaskHandle;
osThreadId LedControlTaskHandle;
osThreadId KeyTaskHandle;
osThreadId DemoControlTaskHandle;
osMessageQId KeyEventQueueHandle;
osMessageQId DisplayDataQueueHandle;
osTimerId LedTimerHandle;
osTimerId BeepTimerHandle;
osTimerId WatchdogTimerHandle;
osTimerId DisplayRefreshTimerHandle;
osMutexId DisplayMutexHandle;
osMutexId SystemMutexHandle;
osSemaphoreId KeySemaphoreHandle;
osSemaphoreId ResourceSemaphoreHandle;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
// 数码管驱动函数声明
void HC595_SendByte(uint8_t data);
void HC138_SelectDigit(uint8_t digit);
void Display_SetDigit(uint8_t digit, uint8_t value);
void Display_SetNumber(uint32_t number);
void Display_SetString(const char* str);
void Display_Clear(void);
void Display_Refresh(void);
/* USER CODE END FunctionPrototypes */

void StartMonitorTask(void const * argument);
void StartDisplayTask(void const * argument);
void StartLedControlTask(void const * argument);
void StartKeyTask(void const * argument);
void StartDemoControlTask(void const * argument);
void LedTimerCallback(void const * argument);
void BeepTimerCallback(void const * argument);
void WatchdogTimerCallback(void const * argument);
void DisplayRefreshCallback(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* GetTimerTaskMemory prototype (linked to static allocation support) */
void vApplicationGetTimerTaskMemory( StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **ppxTimerTaskStackBuffer, uint32_t *pulTimerTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/* USER CODE BEGIN GET_TIMER_TASK_MEMORY */
static StaticTask_t xTimerTaskTCBBuffer;
static StackType_t xTimerStack[configTIMER_TASK_STACK_DEPTH];

void vApplicationGetTimerTaskMemory( StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **ppxTimerTaskStackBuffer, uint32_t *pulTimerTaskStackSize )
{
  *ppxTimerTaskTCBBuffer = &xTimerTaskTCBBuffer;
  *ppxTimerTaskStackBuffer = &xTimerStack[0];
  *pulTimerTaskStackSize = configTIMER_TASK_STACK_DEPTH;
  /* place for user code */
}
/* USER CODE END GET_TIMER_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */
  /* USER CODE BEGIN RTOS_MUTEX */
  /* 暂时注释掉所有RTOS对象，先测试基本任务 */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of MonitorTask */
  osThreadDef(MonitorTask, StartMonitorTask, osPriorityNormal, 0, 256);
  MonitorTaskHandle = osThreadCreate(osThread(MonitorTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  /* 暂时只创建一个任务进行测试 */
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartMonitorTask */
/**
  * @brief  Function implementing the MonitorTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartMonitorTask */
void StartMonitorTask(void const * argument)
{
  /* USER CODE BEGIN StartMonitorTask */
  /* Infinite loop */
  for(;;)
  {
    // 最简单的LED闪烁测试 - 如果看到LED4闪烁说明RTOS正常工作
    HAL_GPIO_WritePin(LED4_pin_GPIO_Port, LED4_pin_Pin, GPIO_PIN_RESET);  // 点亮
    osDelay(500);
    HAL_GPIO_WritePin(LED4_pin_GPIO_Port, LED4_pin_Pin, GPIO_PIN_SET);    // 熄灭
    osDelay(500);
  }
  /* USER CODE END StartMonitorTask */
}

/* USER CODE BEGIN Header_StartDisplayTask */
/**
* @brief Function implementing the DisplayTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartDisplayTask */
void StartDisplayTask(void const * argument)
{
  /* USER CODE BEGIN StartDisplayTask */
  /* 暂时注释掉，只测试一个任务 */
  for(;;)
  {
    osDelay(1000);
  }
  /* USER CODE END StartDisplayTask */
}

/* USER CODE BEGIN Header_StartLedControlTask */
/**
* @brief Function implementing the LedControlTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartLedControlTask */
void StartLedControlTask(void const * argument)
{
  /* USER CODE BEGIN StartLedControlTask */
  /* Infinite loop */
  for(;;)
  {
    // LED3闪烁表示LedControlTask运行 (300ms周期)
    HAL_GPIO_TogglePin(LED3_pin_GPIO_Port, LED3_pin_Pin);
    osDelay(300);
  }
  /* USER CODE END StartLedControlTask */
}

/* USER CODE BEGIN Header_StartKeyTask */
/**
* @brief Function implementing the KeyTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartKeyTask */
void StartKeyTask(void const * argument)
{
  /* USER CODE BEGIN StartKeyTask */
  /* Infinite loop */
  for(;;)
  {
    // LED4闪烁表示KeyTask运行 (200ms周期)
    HAL_GPIO_TogglePin(LED4_pin_GPIO_Port, LED4_pin_Pin);
    osDelay(200);
  }
  /* USER CODE END StartKeyTask */
}

/* USER CODE BEGIN Header_StartDemoControlTask */
/**
* @brief Function implementing the DemoControlTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartDemoControlTask */
void StartDemoControlTask(void const * argument)
{
  /* USER CODE BEGIN StartDemoControlTask */
  uint32_t counter = 0;

  /* Infinite loop */
  for(;;)
  {
    // LED5闪烁表示DemoControlTask运行 (100ms周期)
    HAL_GPIO_TogglePin(LED5_pin_GPIO_Port, LED5_pin_Pin);

    // 暂时注释掉数码管代码，先测试任务运行
    // Display_SetNumber(counter);
    counter++;

    osDelay(100);

    // 每50次重置计数器 (5秒)
    if (counter >= 50) {
      counter = 0;
      // 让LED6亮一下表示重置
      HAL_GPIO_WritePin(LED6_pin_GPIO_Port, LED6_pin_Pin, GPIO_PIN_RESET);
      osDelay(50);
      HAL_GPIO_WritePin(LED6_pin_GPIO_Port, LED6_pin_Pin, GPIO_PIN_SET);
    }
  }
  /* USER CODE END StartDemoControlTask */
}

/* LedTimerCallback function */
void LedTimerCallback(void const * argument)
{
  /* USER CODE BEGIN LedTimerCallback */

  /* USER CODE END LedTimerCallback */
}

/* BeepTimerCallback function */
void BeepTimerCallback(void const * argument)
{
  /* USER CODE BEGIN BeepTimerCallback */

  /* USER CODE END BeepTimerCallback */
}

/* WatchdogTimerCallback function */
void WatchdogTimerCallback(void const * argument)
{
  /* USER CODE BEGIN WatchdogTimerCallback */

  /* USER CODE END WatchdogTimerCallback */
}

/* DisplayRefreshCallback function */
void DisplayRefreshCallback(void const * argument)
{
  /* USER CODE BEGIN DisplayRefreshCallback */
  // 数码管动态扫描刷新
  Display_Refresh();
  /* USER CODE END DisplayRefreshCallback */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/**
 * @brief 74HC595发送一个字节数据
 * @param data 要发送的数据
 */
void HC595_SendByte(uint8_t data)
{
  for (int i = 7; i >= 0; i--) {
    // 设置数据位
    if (data & (1 << i)) {
      HAL_GPIO_WritePin(HC595_DATA_GPIO_Port, HC595_DATA_Pin, GPIO_PIN_SET);
    } else {
      HAL_GPIO_WritePin(HC595_DATA_GPIO_Port, HC595_DATA_Pin, GPIO_PIN_RESET);
    }

    // 产生时钟脉冲
    HAL_GPIO_WritePin(HC595_SCLK_GPIO_Port, HC595_SCLK_Pin, GPIO_PIN_SET);
    HAL_GPIO_WritePin(HC595_SCLK_GPIO_Port, HC595_SCLK_Pin, GPIO_PIN_RESET);
  }

  // 产生锁存脉冲，将数据输出到并行口
  HAL_GPIO_WritePin(HC595_LCLK_GPIO_Port, HC595_LCLK_Pin, GPIO_PIN_SET);
  HAL_GPIO_WritePin(HC595_LCLK_GPIO_Port, HC595_LCLK_Pin, GPIO_PIN_RESET);
}

/**
 * @brief 74HC138选择数码管位
 * @param digit 数码管位 (0-7)
 */
void HC138_SelectDigit(uint8_t digit)
{
  if (digit >= DIGIT_COUNT) return;

  // 设置74HC138的地址线A0, A1, A2
  HAL_GPIO_WritePin(HC138_A0_GPIO_Port, HC138_A0_Pin, (digit & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
  HAL_GPIO_WritePin(HC138_A1_GPIO_Port, HC138_A1_Pin, (digit & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
  HAL_GPIO_WritePin(HC138_A2_GPIO_Port, HC138_A2_Pin, (digit & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);
}

/**
 * @brief 设置指定位数码管显示内容
 * @param digit 数码管位 (0-7)
 * @param value 显示值 (0-15:数字, 0xFF:熄灭, 0xFE:-)
 */
void Display_SetDigit(uint8_t digit, uint8_t value)
{
  if (digit >= DIGIT_COUNT) return;

  if (osMutexWait(DisplayMutexHandle, 100) == osOK) {
    display_buffer[digit] = value;
    osMutexRelease(DisplayMutexHandle);
  }
}

/**
 * @brief 显示32位数字
 * @param number 要显示的数字
 */
void Display_SetNumber(uint32_t number)
{
  if (osMutexWait(DisplayMutexHandle, 100) == osOK) {
    // 清空显示缓冲区
    for (int i = 0; i < DIGIT_COUNT; i++) {
      display_buffer[i] = 0xFF;  // 熄灭
    }

    // 从右到左填充数字
    int pos = DIGIT_COUNT - 1;
    if (number == 0) {
      display_buffer[pos] = 0;  // 显示0
    } else {
      while (number > 0 && pos >= 0) {
        display_buffer[pos] = number % 10;
        number /= 10;
        pos--;
      }
    }

    osMutexRelease(DisplayMutexHandle);
  }
}

/**
 * @brief 显示字符串 (支持0-9, A-F, -, 空格)
 * @param str 要显示的字符串 (最多8个字符)
 */
void Display_SetString(const char* str)
{
  if (str == NULL) return;

  if (osMutexWait(DisplayMutexHandle, 100) == osOK) {
    // 清空显示缓冲区
    for (int i = 0; i < DIGIT_COUNT; i++) {
      display_buffer[i] = 0xFF;  // 熄灭
    }

    int len = strlen(str);
    if (len > DIGIT_COUNT) len = DIGIT_COUNT;

    for (int i = 0; i < len; i++) {
      char c = str[i];
      if (c >= '0' && c <= '9') {
        display_buffer[i] = c - '0';
      } else if (c >= 'A' && c <= 'F') {
        display_buffer[i] = c - 'A' + 10;
      } else if (c >= 'a' && c <= 'f') {
        display_buffer[i] = c - 'a' + 10;
      } else if (c == '-') {
        display_buffer[i] = 0xFE;
      } else {
        display_buffer[i] = 0xFF;  // 空格或其他字符显示为熄灭
      }
    }

    osMutexRelease(DisplayMutexHandle);
  }
}

/**
 * @brief 清空数码管显示
 */
void Display_Clear(void)
{
  if (osMutexWait(DisplayMutexHandle, 100) == osOK) {
    for (int i = 0; i < DIGIT_COUNT; i++) {
      display_buffer[i] = 0xFF;  // 全部熄灭
    }
    osMutexRelease(DisplayMutexHandle);
  }
}

/**
 * @brief 数码管动态扫描刷新 (在定时器中断中调用)
 */
void Display_Refresh(void)
{
  // 先关闭所有数码管 (防止重影)
  HC595_SendByte(0xFF);

  // 选择当前要显示的数码管位
  HC138_SelectDigit(current_digit);

  // 发送当前位的段码数据
  uint8_t segment_code = get_segment_code(display_buffer[current_digit]);
  HC595_SendByte(segment_code);

  // 切换到下一位
  current_digit++;
  if (current_digit >= DIGIT_COUNT) {
    current_digit = 0;
  }
}

/* USER CODE END Application */

