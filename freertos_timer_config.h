/**
 * @file freertos_timer_config.h
 * @brief FreeRTOS软件定时器配置说明
 * <AUTHOR>
 * @date 2025-08-03
 */

#ifndef FREERTOS_TIMER_CONFIG_H
#define FREERTOS_TIMER_CONFIG_H

/* 
 * FreeRTOSConfig.h中软件定时器相关配置项说明
 * 这些配置需要在FreeRTOSConfig.h中设置
 */

/*==============================================================================
 * 软件定时器基本配置
 *============================================================================*/

/**
 * @brief 启用软件定时器功能
 * 设置为1启用软件定时器，设置为0禁用
 * 默认值：0 (禁用)
 */
#define configUSE_TIMERS                    1

/**
 * @brief 定时器任务优先级
 * 定时器服务任务的优先级，通常设置为较高优先级
 * 建议值：比应用任务高1-2级
 * 范围：1 到 (configMAX_PRIORITIES - 1)
 */
#define configTIMER_TASK_PRIORITY           (configMAX_PRIORITIES - 1)

/**
 * @brief 定时器任务栈大小
 * 定时器服务任务的栈大小（以字为单位）
 * 建议值：configMINIMAL_STACK_SIZE * 2
 * 注意：如果定时器回调函数复杂，需要增加栈大小
 */
#define configTIMER_TASK_STACK_DEPTH        (configMINIMAL_STACK_SIZE * 2)

/**
 * @brief 定时器命令队列长度
 * 定时器服务任务的命令队列长度
 * 建议值：10 (可根据实际使用的定时器数量调整)
 * 注意：队列太小可能导致定时器命令丢失
 */
#define configTIMER_QUEUE_LENGTH            10

/*==============================================================================
 * 软件定时器使用注意事项
 *============================================================================*/

/*
 * 1. 回调函数执行时间
 *    - 定时器回调函数应该尽可能短小
 *    - 不要在回调函数中执行阻塞操作
 *    - 不要在回调函数中调用会阻塞的API
 *    - 如需复杂处理，应该发送通知给其他任务处理
 */

/*
 * 2. 定时器精度
 *    - 软件定时器的精度受系统时钟节拍影响
 *    - 最小定时间隔 = 1 / configTICK_RATE_HZ
 *    - 例如：configTICK_RATE_HZ = 1000，最小间隔 = 1ms
 */

/*
 * 3. 内存使用
 *    - 每个定时器占用约 48 字节内存
 *    - 定时器服务任务占用 configTIMER_TASK_STACK_DEPTH * 4 字节栈空间
 *    - 命令队列占用 configTIMER_QUEUE_LENGTH * sizeof(DaemonTaskMessage_t) 字节
 */

/*
 * 4. 定时器服务任务
 *    - FreeRTOS自动创建定时器服务任务
 *    - 所有定时器回调函数在此任务上下文中执行
 *    - 任务名称："Tmr Svc"
 */

/*==============================================================================
 * 常用配置组合示例
 *============================================================================*/

/* 
 * 示例1：基本配置 (适用于简单应用)
 * #define configUSE_TIMERS                 1
 * #define configTIMER_TASK_PRIORITY        3
 * #define configTIMER_TASK_STACK_DEPTH     128
 * #define configTIMER_QUEUE_LENGTH         5
 */

/* 
 * 示例2：高性能配置 (适用于大量定时器)
 * #define configUSE_TIMERS                 1
 * #define configTIMER_TASK_PRIORITY        (configMAX_PRIORITIES - 1)
 * #define configTIMER_TASK_STACK_DEPTH     256
 * #define configTIMER_QUEUE_LENGTH         20
 */

/* 
 * 示例3：低功耗配置 (适用于资源受限系统)
 * #define configUSE_TIMERS                 1
 * #define configTIMER_TASK_PRIORITY        2
 * #define configTIMER_TASK_STACK_DEPTH     96
 * #define configTIMER_QUEUE_LENGTH         3
 */

/*==============================================================================
 * 调试和监控宏定义
 *============================================================================*/

/**
 * @brief 定时器调试信息开关
 * 设置为1启用定时器调试输出
 */
#define TIMER_DEBUG_ENABLE                  1

#if TIMER_DEBUG_ENABLE
    #define TIMER_DEBUG_PRINT(fmt, ...)    printf("[TIMER] " fmt, ##__VA_ARGS__)
#else
    #define TIMER_DEBUG_PRINT(fmt, ...)    
#endif

/**
 * @brief 定时器统计信息结构体
 */
typedef struct {
    uint32_t timer_count;           // 当前活动定时器数量
    uint32_t callback_count;        // 回调函数执行次数
    uint32_t max_callback_time;     // 最大回调执行时间
    uint32_t queue_high_water;      // 命令队列最高水位
} TimerStats_t;

/**
 * @brief 获取定时器统计信息
 * @param pStats 统计信息结构体指针
 */
void vGetTimerStats(TimerStats_t *pStats);

/**
 * @brief 打印定时器统计信息
 */
void vPrintTimerStats(void);

/*==============================================================================
 * 性能优化建议
 *============================================================================*/

/*
 * 1. 合理设置定时器优先级
 *    - 定时器任务优先级应高于普通应用任务
 *    - 但不要设置为最高优先级，避免影响中断响应
 */

/*
 * 2. 优化回调函数
 *    - 保持回调函数简短
 *    - 使用任务通知代替复杂处理
 *    - 避免在回调中使用浮点运算
 */

/*
 * 3. 合理规划定时器数量
 *    - 避免创建过多定时器
 *    - 考虑合并相似功能的定时器
 *    - 使用状态机减少定时器数量
 */

/*
 * 4. 内存管理
 *    - 及时删除不需要的定时器
 *    - 使用静态内存分配（如果支持）
 *    - 监控堆内存使用情况
 */

#endif /* FREERTOS_TIMER_CONFIG_H */
